{"app": {"title": "Galaxy Admin", "subtitle": "Casino Management System"}, "notification": {"markAsRead": "<PERSON>", "emptyNotification": "No new notifications"}, "menu": {"dashboard": "Dashboard", "members": "Members", "orders": "Orders", "management": "Management", "accounting": "Accounting & Reporting", "rolePermission": "Role & Permission", "settings": "Settings"}, "login": {"title": "Sign in to your account", "username": "Username", "usernamePlaceholder": "<PERSON><PERSON> Username", "usernameError": "Please enter your Username", "password": "Password", "passwordPlaceholder": "Enter Password", "passwordError": "Please enter your Password", "logIn": "Log In"}, "dashboard": {"title": "Dashboard", "stats": {"totalMembers": "Total Members", "recentOrders": "Recent Orders", "pendingTransactions": "Pending Transactions", "totalRevenue": "Total Revenue"}, "welcome": "Welcome to Galaxy Admin"}, "members": {"title": "Member Management", "addMember": "Add New Member", "registerMember": "Register Member", "name": "Name", "passport": "Passport", "registrationDate": "Registration Date", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "viewDetails": "View Details", "memberDetails": "Member Details", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "membershipInfo": "Membership Information", "orderHistory": "Order History", "statistics": "Statistics", "totalOrders": "Total Orders", "totalSpent": "Total Spent", "memberSince": "Member Since", "lastVisit": "Last Visit", "membershipTier": "Membership Tier", "email": "Email", "phone": "Phone", "dateOfBirth": "Date of Birth", "nationality": "Nationality", "address": "Address", "notes": "Notes", "recentOrders": "Recent Orders", "noOrders": "No orders found for this member", "tiers": {"bronze": "Bronze", "silver": "Silver", "gold": "Gold", "platinum": "Platinum"}, "form": {"nameRequired": "Name is required", "passportRequired": "Passport is required", "namePlaceholder": "Enter member name", "passportPlaceholder": "Enter passport number"}, "success": {"memberRegistered": "Member registered successfully"}}, "orders": {"title": "Order Management", "orderId": "Order ID", "member": "Member", "amount": "Amount", "status": "Status", "paymentStatus": "Payment Status", "date": "Date", "qrCode": "QR Code", "actions": "Actions", "pending": "Pending", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "processing": "Processing", "statuses": {"pending": "Pending", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "processing": "Processing"}, "orderDetails": "Order Details", "orderInformation": "Order Information", "orderProgress": "Order Progress", "orderCreated": "Order Created", "paymentProcessing": "Payment Processing", "orderCompleted": "Order Completed", "orderItems": "Order Items", "itemName": "Item Name", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "total": "Total", "paymentMethod": "Payment Method", "transactionId": "Transaction ID", "notes": "Notes", "copyQRCode": "Copy QR Code", "refundProcessed": "Refund Processed", "paymentStatuses": {"pending": "Payment Pending", "processing": "Payment Processing", "completed": "Payment Completed", "failed": "Payment Failed"}, "paymentMethods": {"cash": "Cash", "card": "Credit Card", "digital_wallet": "Digital Wallet", "crypto": "Cryptocurrency"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "loading": "Loading...", "noData": "No data available", "on": "On", "off": "Off", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "never": "Never", "showing": "Showing", "orders": "Orders", "print": "Print", "download": "Download", "startDate": "Start Date", "endDate": "End Date", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "actions": "Actions"}, "language": {"english": "English", "taiwan": "繁體中文"}, "management": {"title": "Management", "employees": {"title": "Employee Management", "addEmployee": "Add New Employee", "name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "department": "Department", "status": "Status", "hireDate": "Hire Date", "salary": "Salary", "lastLogin": "Last Login", "performance": "Performance", "attendance": "Attendance", "permissions": "Permissions", "actions": "Actions", "roles": {"admin": "Admin", "manager": "Manager", "cashier": "Cashier", "security": "Security", "maintenance": "Maintenance"}, "departments": {"management": "Management", "gaming": "Gaming", "security": "Security", "maintenance": "Maintenance", "customer_service": "Customer Service"}, "statuses": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "editEmployee": "Edit Employee", "personalInfo": "Personal Information", "basicInfo": "Basic Information", "emergencyContact": "Emergency Contact", "contactName": "Contact Name", "contactPhone": "Contact Phone", "relationship": "Relationship", "address": "Address", "schedule": "Schedule", "day": "Day", "startTime": "Start Time", "endTime": "End Time", "hours": "Hours", "dayOff": "Day Off", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "performanceMetrics": "Performance Metrics", "tasksCompleted": "Tasks Completed", "customerSatisfaction": "Customer Satisfaction", "punctuality": "Punctuality", "teamwork": "Teamwork", "goals": "Goals", "achievements": "Achievements", "attendanceTracking": "Attendance Tracking", "performanceScore": "Performance Score", "attendanceRate": "Attendance Rate", "presentDays": "Present Days", "lateDays": "Late Days", "absentDays": "Absent Days", "avgHours": "Avg Hours", "totalDays": "Total Days", "clockIn": "Clock In", "clockOut": "Clock Out", "totalHours": "Total Hours", "filterByStatus": "Filter by Status", "tableView": "Table View", "calendarView": "Calendar View", "attendanceStatus": {"present": "Present", "late": "Late", "absent": "Absent", "sick": "Sick Leave", "vacation": "Vacation", "half_day": "Half Day"}, "activate": "Activate", "deactivate": "Deactivate", "totalEmployees": "Total Employees", "avgPerformance": "Avg Performance", "avgAttendance": "Avg Attendance", "totalSalary": "Total Salary", "experience": "Experience", "nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "phoneRequired": "Phone is required", "salaryRequired": "Salary is required", "roleRequired": "Role is required", "departmentRequired": "Department is required", "permissionsRequired": "Permissions are required", "selectPermissions": "Select permissions", "departmentDistribution": "Department Distribution", "roleDistribution": "Role Distribution", "statusOverview": "Employee Status Overview", "activeEmployees": "Active Employees", "inactiveEmployees": "Inactive Employees", "suspendedEmployees": "Suspended Employees", "highPerformers": "high performers", "excellentAttendance": "excellent attendance"}, "frontDesk": {"title": "Front Desk / Cash Register Management", "registers": "Cash Registers", "transactions": "Transactions", "shifts": "Shifts", "registerName": "Register Name", "location": "Location", "currentBalance": "Current Balance", "openingBalance": "Opening Balance", "closingBalance": "Closing Balance", "employee": "Employee", "openedAt": "Opened At", "closedAt": "Closed At", "openRegister": "Open Register", "closeRegister": "Close Register", "transactionType": "Type", "amount": "Amount", "description": "Description", "timestamp": "Timestamp", "receiptNumber": "Receipt #", "addTransaction": "Add Transaction", "shiftStart": "Shift Start", "shiftEnd": "Shift End", "totalSales": "Total Sales", "totalRefunds": "Total Refunds", "endShift": "End Shift", "transactionTypes": {"sale": "Sale", "refund": "Refund", "void": "Void", "cash_in": "Cash In", "cash_out": "Cash Out"}, "registerStatuses": {"open": "Open", "closed": "Closed", "maintenance": "Maintenance"}}, "stores": {"title": "Store Management", "addStore": "Add New Store", "storeName": "Store Name", "address": "Address", "manager": "Manager", "totalRevenue": "Total Revenue", "monthlyRevenue": "Monthly Revenue", "employeeCount": "Employee Count", "openingHours": "Opening Hours", "products": "Products", "inventory": "Inventory", "productName": "Product Name", "category": "Category", "price": "Price", "cost": "Cost", "stockLevel": "Stock Level", "minStockLevel": "Min Stock Level", "lastRestocked": "Last Restocked", "updateStock": "Update Stock", "lowStockAlert": "Low Stock Alert", "storeStatuses": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "stats": {"totalEmployees": "Total Employees", "activeEmployees": "Active Employees", "totalStores": "Total Stores", "activeStores": "Active Stores", "totalRegisters": "Total Registers", "openRegisters": "Open Registers", "todayRevenue": "Today's Revenue", "monthlyRevenue": "Monthly Revenue", "lowStockItems": "Low Stock Items", "pendingShifts": "Pending Shifts"}, "exchangeRates": {"search": "Search Fiat...", "fiatCode": "Fiat Code", "spotPrice": "Spot Price", "askPrice": "Ask Price", "bidPrice": "<PERSON><PERSON>", "updatedAt": "Updated At", "autoUpdate": "Auto Update", "updateNote": "Update Note", "actions": "Actions", "editPrice": "Edit Price", "settings": "Settings", "spotPricePlaceholder": "Enter Spot Price", "spotPriceError": "Please enter a valid Spot Price", "askPricePlaceholder": "Enter <PERSON>", "askPriceError": "Please enter a valid Ask Price", "bidPricePlaceholder": "<PERSON><PERSON>", "bidPriceError": "Please enter a valid Bid Price", "updateNotePlaceholder": "Enter Update Note", "password": "Password", "passwordPlaceholder": "Enter your Password", "passwordError": "Password is required", "updateFrequency": "Update Frequency", "updateFrequencyError": "Required", "weekly": "Weekly", "daily": "Daily", "hourly": "Hourly", "updateTime": "Update Time", "updateTimeError": "Required", "updateDay": "Update Day", "updateDayError": "Required"}, "tabs": {"employees": "Employees", "frontDesk": "Front Desk", "stores": "Stores", "rates": "Exchange Rates"}}, "rolePermission": {"title": "Role & Permission Management", "subtitle": "Manage user roles, permissions, and access control", "roles": "Roles", "permissions": "Permissions", "userAssignments": "User Assignments", "createRole": "Create Role", "editRole": "Edit Role", "assignRole": "Assign Role", "roleName": "Role Name", "roleDescription": "Role Description", "status": "Status", "active": "Active", "inactive": "Inactive", "system": "System", "custom": "Custom", "assignedUsers": "Assigned Users", "createdAt": "Created At", "actions": "Actions", "activate": "Activate", "deactivate": "Deactivate", "permissionsCount": "permissions", "user": "user", "users": "users", "assignments": "assignments", "deleteConfirmTitle": "Delete Role", "deleteConfirmDescription": "Are you sure you want to delete this role? This action cannot be undone.", "cannotDeleteWithUsers": "Cannot delete role with assigned users", "removeRoleConfirmTitle": "Remove Role", "removeRoleConfirmDescription": "Are you sure you want to remove this role from the user?", "cannotRemoveSystemRole": "Cannot remove system role", "removeRole": "Remove Role", "totalRoles": "Total Roles", "totalPermissions": "Total Permissions", "avgPermissions": "Avg Permissions", "searchRoles": "Search roles...", "searchPermissions": "Search permissions...", "searchUsers": "Search users...", "filterByStatus": "Filter by Status", "filterByRole": "Filter by Role", "filterByCategory": "Filter by Category", "categories": "categories", "totalActions": "Total Actions", "availableActions": "Available Actions", "noPermissionsFound": "No Permissions Found", "tryDifferentSearch": "Try adjusting your search criteria", "assignedRole": "Assigned Role", "assignedAt": "Assigned At", "assignedBy": "Assigned By", "basicInformation": "Basic Information", "roleNameRequired": "Role name is required", "roleNameMinLength": "Role name must be at least 2 characters", "roleNameMaxLength": "Role name cannot exceed 50 characters", "roleNamePlaceholder": "Enter role name", "roleDescriptionRequired": "Role description is required", "roleDescriptionMaxLength": "Description cannot exceed 200 characters", "roleDescriptionPlaceholder": "Enter role description", "selectPermissions": "Select Permissions", "selectPermissionsDescription": "Choose the permissions that users with this role will have", "selected": "selected", "systemRoleWarning": "System Role", "systemRoleWarningDescription": "This is a system role. Some properties cannot be modified.", "roleInformation": "Role Information", "currentPermissions": "Current Permissions", "updatePermissions": "Update Permissions", "permissionChangeWarning": "Permission Change Warning", "permissionChangeWarningDescription": "Changing permissions will affect {{count}} users with this role.", "assignRoleInfo": "Assign Role to User", "assignRoleInfoDescription": "Select a user and role to create a new role assignment", "selectUser": "Select User", "selectUserPlaceholder": "Choose a user", "userRequired": "User selection is required", "selectRole": "Select Role", "selectRolePlaceholder": "Choose a role", "roleRequired": "Role selection is required", "roleDetails": "Role Details"}, "accounting": {"title": "Accounting & Reporting", "subtitle": "Financial statements, transaction summaries, and reconciliation reports", "error": "Failed to load accounting data", "errorDescription": "Please try again later or contact support", "financialStatements": "Financial Statements", "transactionSummaries": "Transaction Summaries", "reconciliationReports": "Reconciliation Reports", "selectReportType": "Select Report Type", "incomeStatement": "Income Statement", "balanceSheet": "Balance Sheet", "cashFlow": "Cash Flow Statement", "trialBalance": "Trial Balance", "income_statement": "Income Statement", "balance_sheet": "Balance Sheet", "cash_flow": "Cash Flow Statement", "trial_balance": "Trial Balance", "totalRevenue": "Total Revenue", "totalExpenses": "Total Expenses", "netIncome": "Net Income", "reconciliationStatus": "Reconciliation Status", "balanced": "Balanced", "variance": "<PERSON><PERSON><PERSON>", "pending": "Pending", "financialHealth": "Financial Health", "assets": "Assets", "liabilities": "Liabilities", "equity": "Equity", "statementType": "Statement Type", "period": "Period", "revenue": "Revenue", "expenses": "Expenses", "generatedAt": "Generated At", "selectStatementType": "Select Statement Type", "selectPeriod": "Select Period", "generateStatement": "Generate Statement", "statementDetails": "Statement Details", "gaming": "Gaming", "food": "Food", "beverages": "Beverages", "other": "Other", "salaries": "Salaries", "utilities": "Utilities", "maintenance": "Maintenance", "supplies": "Supplies", "marketing": "Marketing", "profitMargin": "<PERSON><PERSON>", "cash": "Cash", "inventory": "Inventory", "equipment": "Equipment", "totalAssets": "Total Assets", "accountsPayable": "Accounts Payable", "loans": "Loans", "totalLiabilities": "Total Liabilities", "capital": "Capital", "retainedEarnings": "Retained Earnings", "totalEquity": "Total Equity", "periods": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly"}, "totalTransactions": "Total Transactions", "totalAmount": "Total Amount", "averageTransaction": "Average Transaction", "generateSummary": "Generate Summary", "summaryDetails": "Summary Details", "transactionSummary": "Transaction Summary", "transactionRate": "Transaction Rate", "detailedBreakdown": "Detailed Breakdown", "selectStatementTypeFirst": "Please select a statement type first", "selectRegisterFirst": "Please select a register first", "generateFinancialStatement": "Generate Financial Statement", "configureStatementParameters": "Configure statement generation parameters", "generateTransactionSummary": "Generate Transaction Summary", "configureSummaryParameters": "Configure summary generation parameters", "generateReconciliationReport": "Generate Reconciliation Report", "configureReportParameters": "Configure report generation parameters", "statementTypeRequired": "Statement type is required", "periodType": "Period Type", "reportingPeriod": "Reporting Period", "dateRangeRequired": "Date range is required", "additionalOptions": "Additional Options", "includeComparisons": "Include Period Comparisons", "includeComparisonsDescription": "Compare with previous period data", "includeNotes": "Include Explanatory Notes", "includeNotesDescription": "Add detailed explanations for major variances", "detailedBreakdownDescription": "Include detailed category breakdowns", "summaryPeriod": "Summary Period", "includeBreakdowns": "Include Breakdowns", "byTransactionTypeDescription": "Break down by sale, refund, void, etc.", "byPaymentMethodDescription": "Break down by cash, card, digital wallet, etc.", "byEmployeeDescription": "Break down by individual employees", "byRegisterDescription": "Break down by cash registers", "filterOptions": "Filter Options", "minimumAmount": "Minimum Amount", "selectMinimumAmount": "Select minimum amount", "noMinimum": "No minimum", "includeTransactionTypes": "Include Transaction Types", "selectTransactionTypes": "Select transaction types", "cashRegister": "Cash Register", "registerRequired": "Register selection is required", "reconciliationPeriod": "Reconciliation Period", "shiftDetails": "Shift Details", "expectedClosingBalance": "Expected Closing Balance", "shiftNotes": "Shift Notes", "shiftNotesPlaceholder": "Add any notes about this shift...", "reconciliationOptions": "Reconciliation Options", "includeDiscrepancies": "Include Discrepancy Analysis", "includeDiscrepanciesDescription": "Analyze and report any cash discrepancies", "autoReconcile": "Auto-reconcile if balanced", "autoReconcileDescription": "Automatically mark as reconciled if no variance", "varianceTolerance": "Variance Tolerance", "selectVarianceTolerance": "Select acceptable variance", "noTolerance": "No tolerance (exact match)", "byTransactionType": "By Transaction Type", "byPaymentMethod": "By Payment Method", "byEmployee": "By Employee", "byRegister": "By Register", "register": "Register", "expectedBalance": "Expected Balance", "actualBalance": "Actual Balance", "selectRegister": "Select Register", "selectStatus": "Select Status", "generateReport": "Generate Report", "reportDetails": "Report Details", "reconciliationReport": "Reconciliation Report", "overage": "Overage", "shortage": "Shortage", "varianceAmount": "<PERSON><PERSON><PERSON> Amount", "openingBalance": "Opening Balance", "transactionBreakdown": "Transaction Breakdown", "sales": "Sales", "refunds": "Refunds", "cashIn": "Cash In", "cashOut": "Cash Out", "voids": "Voids", "discrepancies": "Discrepancies", "reconciliationInfo": "Reconciliation Information", "reconciledBy": "Reconciled By", "reconciledAt": "Reconciled At", "notes": "Notes", "reconcile": "Reconcile", "reconcileReport": "Reconcile Report", "actualBalanceRequired": "Actual balance is required", "reconciliationNotes": "Add reconciliation notes...", "reconciliationStatuses": {"balanced": "Balanced", "variance": "<PERSON><PERSON><PERSON>", "pending": "Pending"}, "transactionId": "Transaction ID", "timestamp": "Timestamp", "paymentMethod": "Payment Method", "employee": "Employee", "transactionDetails": "Transaction Details", "viewTransactionDetails": "View Transaction Details", "noTransactionsFound": "No transactions found for this type", "amount": "Amount", "description": "Description", "reference": "Reference"}, "transactions": {"types": {"sale": "Sale", "refund": "Refund", "void": "Void", "cash_in": "Cash In", "cash_out": "Cash Out"}}, "settings": {"title": "Settings", "subtitle": "Configure system preferences and options", "systemStatus": {"title": "System Status: Online", "description": "All systems are operational and running smoothly."}, "tabs": {"general": "General", "profile": "Profile", "security": "Security", "notifications": "Notifications", "language": "Language", "system": "System", "backup": "Backup", "performance": "Performance"}, "general": {"siteInfo": "Site Information", "siteName": "Site Name", "siteNameRequired": "Site name is required", "siteDescription": "Site Description", "timezone": "Timezone", "timezoneRequired": "Timezone is required", "dateFormat": "Date Format", "timeFormat": "Time Format", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencySymbol": "Currency Symbol", "decimalPlaces": "Decimal Places", "businessHours": "Business Hours", "start": "Start", "end": "End", "systemOptions": "System Options", "maintenanceMode": "Maintenance Mode", "maintenanceModeDesc": "Put the system in maintenance mode", "debugMode": "Debug Mode", "debugModeDesc": "Enable detailed error logging", "autoSave": "Auto Save", "autoSaveInterval": "Auto Save Interval", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Failed to save settings", "resetSuccess": "Settings reset to defaults", "regionalSettings": "Regional Settings", "dateFormatDesc": "How dates are displayed in your locale", "timeFormatDesc": "How times are displayed in your locale", "dateFormatRequired": "Date format is required", "timeFormatRequired": "Time format is required", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "currencyRequired": "Currency is required", "currencySymbolRequired": "Currency symbol is required", "decimalPlacesRequired": "Decimal places is required", "openingTime": "Opening Times", "closingTime": "Closing Times", "minutes": "Minutes", "autoSaveDesc": "Automatically save changes every x minutes", "reset": "Reset to Defaults", "saveChanges": "Save Changes"}, "language": {"currentLanguage": "Current Language", "translationProgress": "Translation", "regionalSettings": "Regional Settings", "dateFormat": "Date Format", "dateFormatDesc": "How dates are displayed in your locale", "timeFormat": "Time Format", "timeFormatDesc": "How times are displayed in your locale", "translationStatus": "Translation Status", "translationStatusDesc": "Current progress of translations for supported languages", "changeNote": "Language changes will be applied immediately", "applyLanguage": "Apply Language", "saveSuccess": "Language settings saved successfully", "saveError": "Failed to save language settings"}, "system": {"systemStatus": "System Status", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "diskUsage": "Disk Usage", "networkUsage": "Network Usage", "uptime": "Uptime", "activeConnections": "Active Connections", "errorRate": "Error Rate", "serverName": "Server Name", "maxConnections": "Max Connections", "connectionTimeout": "Connection Timeout", "enableLogging": "Enable Logging", "logLevel": "Log Level", "enableCaching": "Enable Caching", "cacheSize": "<PERSON><PERSON>", "enableCompression": "Enable Compression", "compressionLevel": "Compression Level", "enableSSL": "Enable SSL", "sslPort": "SSL Port", "saveSuccess": "System settings saved successfully", "saveError": "Failed to save system settings", "restartWarning": "System restart required for changes to take effect", "restartServer": "Restart Server", "serverConfig": "Server Configuration", "securityConfig": "Security Configuration", "enableSSLDesc": "Encrypt data in transit", "securityWarning": "Security Warning", "securityWarningDesc": "Ensure proper SSL certificates are in place before enabling", "seconds": "Seconds", "loggingConfig": "Logging Configuration", "enableLoggingDesc": "Record system activity for debugging and auditing", "performanceConfig": "Performance Configuration", "enableCachingDesc": "Store frequently accessed data in memory", "enableCompressionDesc": "Reduce data size for faster transfers", "balanced": "Balanced", "fast": "Fast", "fastest": "Fastest", "best": "Best", "saveChanges": "Save Changes"}, "notifications": {"deliveryMethods": "Delivery Methods", "email": "Email Notifications", "emailDesc": "Receive notifications via email", "sms": "SMS Notifications", "smsDesc": "Receive notifications via SMS", "push": "Push Notifications", "pushDesc": "Receive notifications via browser/app", "sound": "Sound Notifications", "soundDesc": "Play sound when notifications arrive", "quietHours": "Quiet Hours", "quietHoursDesc": "Do not disturb during these hours", "start": "Start Time", "end": "End Time", "notificationTypes": "Notification Types", "frequency": "Notification Frequency", "immediate": "Immediate", "daily": "Daily Digest", "weekly": "Weekly Summary", "saveSuccess": "Notification settings saved successfully", "saveError": "Failed to save notification settings", "types": {"newOrders": "New Orders", "memberRegistrations": "Member Registrations", "systemAlerts": "System Alerts", "securityAlerts": "Security Alerts", "paymentAlerts": "Payment Alerts", "employeeUpdates": "Employee Updates", "maintenanceAlerts": "Maintenance Alerts", "reportGeneration": "Report Generation"}, "descriptions": {"newOrders": "Notify when new orders are placed", "memberRegistrations": "Notify when new members register", "systemAlerts": "Important system status updates", "securityAlerts": "Security-related notifications", "paymentAlerts": "Payment processing notifications", "employeeUpdates": "Employee status changes", "maintenanceAlerts": "System maintenance notifications", "reportGeneration": "When reports are generated"}, "soundSettings": "Sound Settings", "soundEnabled": "Enable Sound Notifications", "soundEnabledDesc": "Play sound when notifications arrive", "quietStart": "Quiet Hours Start", "quietEnd": "Quiet Hours End", "immediateDesc": "Receive notifications immediately", "dailyDesc": "Receive a daily summary of notifications", "weeklyDesc": "Receive a weekly summary of notifications", "saveChanges": "Save Changes"}, "profile": {"personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "position": "Position", "department": "Department", "dateOfBirth": "Date of Birth", "address": "Address", "bio": "Bio", "uploadAvatar": "Upload Avatar", "changeAvatar": "Change Avatar", "saveSuccess": "Profile saved successfully", "saveError": "Failed to save profile", "profilePicture": "Profile Picture", "avatarFormatError": "Please upload a JPEG or PNG image", "avatarSizeError": "Image must be smaller than 2MB", "avatarUploadSuccess": "Avatar uploaded successfully", "avatarDescription": "Upload a JPEG or PNG image smaller than 2MB", "avatarUploadError": "Failed to upload avatar", "workInfo": "Work Information", "employeeId": "Employee ID", "accountStats": "Account Statistics", "yearsActive": "Years Active", "loginSessions": "<PERSON><PERSON>", "uptime": "Uptime", "saveChanges": "Save Changes"}, "security": {"passwordSettings": "Password Settings", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDesc": "Require a verification code at login", "sessionTimeout": "Session Timeout", "sessionTimeoutDesc": "Minutes of inactivity before logout", "passwordExpiry": "Password Expiry", "passwordExpiryDesc": "Days before password must be changed", "loginNotifications": "Login Notifications", "loginNotificationsDesc": "Notify on new login attempts", "ipWhitelist": "IP Whitelist", "ipWhitelistDesc": "Only allow logins from specific IPs", "allowedIPs": "Allowed IP Addresses", "activeSessions": "Active Sessions", "device": "<PERSON><PERSON>", "location": "Location", "lastActive": "Last Active", "terminateSession": "Terminate", "terminateAll": "Terminate All Sessions", "saveSuccess": "Security settings saved successfully", "saveError": "Failed to save security settings", "passwordMismatch": "Passwords do not match", "changePassword": "Change Password", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "confirmPasswordRequired": "Confirm password is required", "passwordRequirements": "For security reasons, please use a strong password", "passwordRequirementsDesc": "Password must be at least 8 characters and contain a mix of uppercase, lowercase, numbers, and special characters", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm your new password", "enable2FA": "Enable Two-Factor Authentication", "enable2FADesc": "Require a verification code at login", "authenticatorApp": "Authenticator App", "authenticatorAppDesc": "Scan the QR code with your authenticator app", "setupAuthenticator": "<PERSON>up Authenticator", "sessionManagement": "Session Management", "minutes": "Minutes", "hours": "Hours", "days": "Days", "activeSessionsDesc": "Manage and terminate active user sessions", "manageSessions": "Manage Sessions", "terminateSessionConfirm": "Are you sure you want to terminate this session?", "ipAddress": "IP Address", "lastActivity": "Last Activity", "status": "Status", "sessionStatus": {"active": "Active", "expired": "Expired"}, "enableIPWhitelist": "Enable IP Whitelist", "enableIPWhitelistDesc": "Only allow logins from specific IPs", "saveChanges": "Save Changes"}, "backup": {"autoBackup": "Automatic Backup", "autoBackupDesc": "Schedule regular system backups", "backupFrequency": "Backup Frequency", "backupTime": "Backup Time", "retentionDays": "Retention Period (Days)", "backupOptions": "Backup Options", "includeFiles": "Include Files", "includeDatabase": "Include Database", "includeSettings": "Include Settings", "cloudBackup": "Cloud Backup", "cloudBackupDesc": "Store backups in the cloud", "cloudProvider": "Cloud Provider", "backupNow": "Backup Now", "restoreBackup": "Restore Backup", "backupHistory": "Backup History", "backupName": "Backup Name", "backupSize": "Size", "backupDate": "Date", "backupStatus": "Status", "download": "Download", "restore": "Rest<PERSON>", "delete": "Delete", "saveSuccess": "Backup settings saved successfully", "saveError": "Failed to save backup settings", "backupSuccess": "Backup completed successfully", "backupError": "Backup failed", "totalBackups": "Total Backups", "lastBackupSize": "Last Backup Size", "successRate": "Success Rate", "createBackup": "Create Backup", "automaticBackup": "Automatic Backup", "enableAutoBackup": "Enable Automatic Backup", "enableAutoBackupDesc": "Automatically backup the system on a schedule", "saveChanges": "Save Changes", "frequencies": {"hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "days": "Days", "frequency": "Frequency", "backupContent": "Backup Content", "includeFilesDesc": "Include system files in backup", "includeDatabaseDesc": "Include database in backup", "includeSettingsDesc": "Include system settings in backup", "enableCloudBackup": "Enable Cloud Backup", "enableCloudBackupDesc": "Store backups in the cloud", "cloudWarning": "Cloud backup is experimental", "cloudWarningDesc": "Ensure proper cloud credentials are set before enabling", "type": "Type", "size": "Size", "date": "Date", "status": "Status", "duration": "Duration", "includes": "Includes", "types": {"auto": "Automatic", "manual": "Manual"}, "statuses": {"pending": "Pending", "in_progress": "In Progress", "completed": "Completed", "failed": "Failed"}}, "performance": {"optimizationSettings": "Optimization Settings", "enableOptimization": "Enable Performance Optimization", "cacheLevel": "<PERSON>ache <PERSON>", "compressionEnabled": "Enable Compression", "compressionLevel": "Compression Level", "lazyLoading": "Enable Lazy Loading", "imageOptimization": "Image Optimization", "minifyAssets": "Minify Assets", "enableCDN": "Enable CDN", "requestSettings": "Request Settings", "maxConcurrentRequests": "Max Concurrent Requests", "requestTimeout": "Request Timeout (seconds)", "enablePrefetch": "Enable Prefetching", "enableServiceWorker": "Enable Service Worker", "performanceMetrics": "Performance Metrics", "pageLoadTime": "Page Load Time", "firstContentfulPaint": "First Contentful Paint", "largestContentfulPaint": "Largest Contentful Paint", "cumulativeLayoutShift": "Cumulative Layout Shift", "firstInputDelay": "First Input Delay", "performanceScore": "Performance Score", "saveSuccess": "Performance settings saved successfully", "saveError": "Failed to save performance settings"}}}