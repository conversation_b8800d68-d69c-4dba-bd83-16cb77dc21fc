import { Table } from 'antd';
import type { TableProps } from 'antd';
import { sortByDate } from '@/utils/tableUtils.ts';
import type { GalaxyTableProps } from '@/types';

const GalaxyTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  customSort = false,
  pagination,
  ...props
}: GalaxyTableProps<T> & Omit<TableProps<T>, 'dataSource' | 'columns'>) => {
  // Apply custom sorting if enabled
  const processedColumns = customSort
    ? columns.map((column) => {
        if (
          column.dataIndex === 'registrationDate' ||
          column.dataIndex === 'date'
        ) {
          return {
            ...column,
            sorter: (a: T, b: T) =>
              sortByDate(a[column.dataIndex], b[column.dataIndex]),
            sortDirections: ['descend', 'ascend'] as const,
            defaultSortOrder: 'descend' as const,
          };
        }
        return column;
      })
    : columns;

  return (
    <div className='galaxy-table-container'>
      <Table<T>
        dataSource={data}
        columns={processedColumns}
        loading={loading}
        pagination={
          pagination
            ? {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: pagination.onChange,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }
            : false
        }
        className='shadow-md rounded-lg'
        scroll={{ x: 'max-content' }}
        size='middle'
        {...props}
      />
    </div>
  );
};

export default GalaxyTable;
