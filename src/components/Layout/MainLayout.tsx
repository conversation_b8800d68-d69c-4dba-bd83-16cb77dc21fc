import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useResponsive } from '@/hooks/useResponsive.ts';
import Sidebar from './Sidebar';
import Header from './Header';
import {useNavigation} from "@/hooks/useNavigation.ts";

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const {getCurrentPage} = useNavigation();

  // Auto-collapse sidebar on mobile and tablet
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    } else if (isTablet) {
      setCollapsed(true);
    } else {
      setCollapsed(false);
    }
  }, [isMobile, isTablet]);

  const toggleSidebar = () => {
    if (isMobile) {
      setMobileMenuOpen(!mobileMenuOpen);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const closeMobileMenu = () => {
    if (isMobile) {
      setMobileMenuOpen(false);
    }
  };


  // Get page title based on current route
  const getPageTitle = () => {
    const pageKey = getCurrentPage();
    switch (pageKey) {
      case 'dashboard':
        return t('dashboard.title');
      case 'members':
        return t('members.title');
      case 'orders':
        return t('orders.title');
      case 'management':
        return t('management.title');
      default:
        return t('dashboard.title');
    }
  };

  return (
    <Layout className='min-h-screen'>
      {/* Mobile Overlay */}
      {isMobile && mobileMenuOpen && (
        <div
          className='fixed inset-0 bg-black bg-opacity-50 z-40'
          onClick={closeMobileMenu}
        />
      )}

      <Sidebar
        collapsed={collapsed}
        selectedKey={getCurrentPage()}
        isMobile={isMobile}
        mobileMenuOpen={mobileMenuOpen}
        onMobileMenuClose={closeMobileMenu}
      />

      <Layout className={isMobile ? 'ml-0' : collapsed ? 'ml-20' : 'ml-70'} style={{ marginLeft: isMobile ? 0 : collapsed ? 80 : 280 }}>
        <Header
          collapsed={collapsed}
          onToggle={toggleSidebar}
          title={getPageTitle()}
          isMobile={isMobile}
          isTablet={isTablet}
        />
        <Content className={`${isMobile ? 'p-3' : isTablet ? 'p-4' : 'p-6'} bg-gray-50`}>
          <div className={`bg-white rounded-lg shadow-sm ${isMobile ? 'p-3' : isTablet ? 'p-4' : 'p-6'} min-h-full`}>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
