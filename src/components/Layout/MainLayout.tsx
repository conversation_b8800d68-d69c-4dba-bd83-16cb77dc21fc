import React, { useState } from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Sidebar from './Sidebar';
import Header from './Header';
import {useNavigation} from "@/hooks/useNavigation.ts";

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { t } = useTranslation();

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };
  const {getCurrentPage} = useNavigation();


  // Get page title based on current route
  const getPageTitle = () => {
    const pageKey = getCurrentPage();
    switch (pageKey) {
      case 'dashboard':
        return t('dashboard.title');
      case 'members':
        return t('members.title');
      case 'orders':
        return t('orders.title');
      case 'management':
        return t('management.title');
      default:
        return t('dashboard.title');
    }
  };

  return (
    <Layout className='min-h-screen'>
      <Sidebar collapsed={collapsed} selectedKey={getCurrentPage()} />
      <Layout>
        <Header
          collapsed={collapsed}
          onToggle={toggleSidebar}
          title={getPageTitle()}
        />
        <Content className='p-6 bg-gray-50'>
          <div className='bg-white rounded-lg shadow-sm p-6 min-h-full'>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
