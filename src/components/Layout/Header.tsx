import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  SearchOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { Layout, Button, Typography, Avatar, Dropdown } from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import NotificationDropdown from './Notification/NotificationDropdown';
import { useLogout } from '@/hooks/useLogin';
import {useNavigation} from "@/hooks/useNavigation.ts";

const { Header: AntHeader } = Layout;
const { Title, Text } = Typography;

interface HeaderProps {
  collapsed: boolean;
  onToggle: () => void;
  title: string;
}

const Header: React.FC<HeaderProps> = ({ collapsed, onToggle, title }) => {
  const { t } = useTranslation();
  const logout = useLogout();
  const {navigateTo} = useNavigation();
  const userMenuItems = useMemo(
    () => [
      {
        key: 'profile',
        icon: <UserOutlined className='text-gray-600' />,
        label: <span className='text-gray-700 font-medium'>View Profile</span>,
        onClick: () => navigateTo.profile(),
      },
      {
        key: 'settings',
        icon: <SettingOutlined className='text-gray-600' />,
        label: <span className='text-gray-700 font-medium'>Settings</span>,
        onClick: () => navigateTo.settings(),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'logout',
        icon: <LogoutOutlined className='text-red-500' />,
        label: <span className='text-red-500 font-medium'>Sign Out</span>,
        onClick: logout,
      },
    ],
    [logout],
  );

  return (
    <AntHeader
      style={{
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(12px)',
        borderBottom: '1px solid #f0f0f0',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        height: '96px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'sticky',
        zIndex: 100,
        top: '0px',
      }}
    >
      {/* Left Section */}
      <div className='flex items-center gap-6'>
        <Button
          type='text'
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggle}
          style={{
            width: '44px',
            height: '44px',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
            color: '#4b5563',
            transition: 'all 0.2s ease',
          }}
          className='hover:bg-gray-50 hover:text-gray-900'
        />

        <div className='flex items-center gap-4'>
          <div
            style={{
              width: '4px',
              height: '32px',
              background: 'linear-gradient(180deg, #1890ff 0%, #096dd9 100%)',
              borderRadius: '2px',
            }}
          ></div>
          <div className='flex flex-col justify-center'>
            <Title
              level={4}
              style={{ margin: 0, color: '#1f2937', fontWeight: 600 }}
            >
              <span>{title}</span>
            </Title>
            <Text>
              <span className='text-xs text-gray-500 font-medium'>
                {t('app.subtitle')}
              </span>
            </Text>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className='flex items-center gap-x-2'>
        {/* Search Button */}
        <Button
          type='text'
          icon={<SearchOutlined />}
          size='large'
          className='hover:bg-gray-50'
        />

        {/* Notifications */}
        <NotificationDropdown />

        {/* Divider */}
        <div
          style={{
            width: '1px',
            height: '24px',
            backgroundColor: '#e5e7eb',
            margin: '0 8px',
          }}
        ></div>

        {/* User Menu */}
        <Dropdown
          menu={{
            items: userMenuItems,
            style: {
              borderRadius: '12px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              border: '1px solid #f0f0f0',
              padding: '8px',
            },
          }}
          placement='bottomCenter'
          trigger={['click']}
          arrow={{ pointAtCenter: true }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              cursor: 'pointer',
              padding: '8px 12px',
              borderRadius: '12px',
              transition: 'all 0.2s ease',
              border: '1px solid transparent',
            }}
            className='hover:bg-gray-50 hover:border-gray-200'
          >
            <Avatar
              size={36}
              icon={<UserOutlined />}
              style={{
                background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              }}
            />
            <div className='hidden  md:flex gap-1 flex-col'>
              <Text>
                <span className='text-sm font-semibold text-gray-900 block leading-tight'>
                  Admin User
                </span>
              </Text>
              <Text>
                <span className='text-xs text-gray-500 leading-tight'>
                  Administrator
                </span>
              </Text>
            </div>
            <DownOutlined
              style={{
                fontSize: '10px',
                color: '#9ca3af',
                transition: 'transform 0.2s ease',
              }}
              className='hidden md:block'
            />
          </div>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
