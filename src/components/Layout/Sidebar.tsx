import React from 'react';
import { Layout, Menu, Select, Typography, Divider } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  SettingOutlined,
  CalculatorOutlined,
  GlobalOutlined,
  StarFilled,
  SafetyOutlined, TeamOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ROUTES } from '@/constants/routes.ts';
import type { MenuItem, Language } from '@/types';

const { Sider } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

interface SidebarProps {
  collapsed: boolean;
  selectedKey: string;
  isMobile?: boolean;
  mobileMenuOpen?: boolean;
  onMobileMenuClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  selectedKey,
  isMobile = false,
  mobileMenuOpen = false,
  onMobileMenuClose
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const handleMenuSelect = (key: string) => {
    const routes: Record<string, string> = {
      dashboard: ROUTES.DASHBOARD,
      members: ROUTES.MEMBERS,
      orders: ROUTES.ORDERS,
      management: ROUTES.MANAGEMENT,
      accounting: ROUTES.ACCOUNTING,
      rolePermission: ROUTES.ROLE_PERMISSION,
      settings: ROUTES.SETTINGS,
    };

    if (routes[key]) {
      navigate(routes[key]);
      // Close mobile menu after navigation
      if (isMobile && onMobileMenuClose) {
        onMobileMenuClose();
      }
    }
  };

  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: t('menu.dashboard'),
      icon: <DashboardOutlined />,
      path: ROUTES.DASHBOARD,
    },
    {
      key: 'members',
      label: t('menu.members'),
      icon: <UserOutlined />,
      path: ROUTES.MEMBERS,
    },
    {
      key: 'orders',
      label: t('menu.orders'),
      icon: <ShoppingCartOutlined />,
      path: ROUTES.ORDERS,
    },
    {
      key: 'management',
      label: t('menu.management'),
      icon: <TeamOutlined />,
      path: ROUTES.MANAGEMENT,
    },
    {
      key: 'accounting',
      label: t('menu.accounting'),
      icon: <CalculatorOutlined />,
      path: ROUTES.ACCOUNTING,
    },
    {
      key: 'rolePermission',
      label: t('menu.rolePermission'),
      icon: <SafetyOutlined />,
      path: ROUTES.ROLE_PERMISSION,
    },
    {
      key: 'settings',
      label: t('menu.settings'),
      icon: <SettingOutlined />,
      path: ROUTES.SETTINGS,
    },
  ];

  const handleLanguageChange = (language: Language) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
  };

  // Determine sidebar visibility and styling based on device type
  const sidebarStyle = isMobile ? {
    position: 'fixed' as const,
    top: '0px',
    left: mobileMenuOpen ? '0px' : '-280px',
    transition: 'left 0.3s ease',
    zIndex: 50,
  } : {
    position: 'fixed' as const,
    top: '0px',
    left: '0px',
  };

  const sidebarWidth = isMobile ? 280 : collapsed ? 80 : 280;

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={isMobile ? false : collapsed}
      style={sidebarStyle}
      className={`sidebar-dark h-screen max-h-screen shadow-2xl ${isMobile ? 'z-50' : 'z-40'}`}
      width={sidebarWidth}
    >
      {/* Logo Section */}
      <div className={`${isMobile ? 'px-4 py-4' : 'px-6 py-6'} border-b border-white/10`}>
        {(!collapsed || isMobile) ? (
          <div className='flex items-center gap-3'>
            <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg`}>
              <StarFilled className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`} />
            </div>
            <div>
              <Title level={4} className={`m-0 font-bold ${isMobile ? 'text-base' : ''}`}>
                <span className='text-white'>{t('app.title')}</span>
              </Title>
              <Text>
                <span className={`${isMobile ? 'text-xs' : 'text-xs'} font-medium text-slate-300`}>
                  Casino Management
                </span>
              </Text>
            </div>
          </div>
        ) : (
          <div className='flex justify-center'>
            <div className='w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg'>
              <StarFilled className='text-white text-lg' />
            </div>
          </div>
        )}
      </div>

      {/* Navigation Menu */}
      <div className={`${isMobile ? 'px-2 py-3' : 'px-3 py-4'}`}>
        {(!collapsed || isMobile) && (
          <Text className=''>
            <span className={`text-xs font-semibold text-slate-400 uppercase tracking-wider ${isMobile ? 'px-2' : 'px-3'} mb-3 block`}>
              Navigation
            </span>
          </Text>
        )}
        <Menu
          mode='inline'
          selectedKeys={[selectedKey]}
          className='border-r-0 bg-transparent'
          items={menuItems.map((item) => ({
            key: item.key,
            icon: <div className={`${isMobile ? 'text-base' : 'text-lg'}`}>{item.icon}</div>,
            label: <span className={`font-medium text-white ${isMobile ? 'text-sm' : ''}`}>{item.label}</span>,
            onClick: () => handleMenuSelect(item.key),
            className: `${isMobile ? 'mb-1' : 'mb-1'} rounded-lg mx-0 ${selectedKey === item.key ? 'bg-primary/10 border-r-2 border-primary' : 'hover:bg-gray-50'}`,
          }))}
        />
      </div>

      {/* Language Selector */}
      {(!collapsed || isMobile) && (
        <div className={`absolute ${isMobile ? 'bottom-4 left-3 right-3' : 'bottom-6 left-4 right-4'}`}>
          <Divider className='my-4' />
          <div className={`bg-white rounded-lg ${isMobile ? 'p-3' : 'p-4'}`}>
            <div className='flex items-center gap-2 mb-3'>
              <GlobalOutlined />
              <Text className='text-sm font-medium text-white'>
                <span className='text-xs font-semibold text-slate-400 uppercase tracking-wider'>
                  Language
                </span>
              </Text>
            </div>
            <Select
              value={i18n.language as Language}
              onChange={handleLanguageChange}
              className='w-full'
              size={isMobile ? 'small' : 'middle'}
              variant='borderless'
            >
              <Option value='en'>
                <div className='flex items-center gap-2'>
                  <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>🇺🇸</span>
                  <span className={`${isMobile ? 'text-xs' : ''}`}>{t('language.english')}</span>
                </div>
              </Option>
              <Option value='tw'>
                <div className='flex items-center gap-2'>
                  <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>🇹🇼</span>
                  <span className={`${isMobile ? 'text-xs' : ''}`}>{t('language.taiwan')}</span>
                </div>
              </Option>
            </Select>
          </div>
        </div>
      )}
    </Sider>
  );
};

export default Sidebar;
