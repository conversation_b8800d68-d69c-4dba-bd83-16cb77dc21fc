import { Button, Flex, Typography } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { NotificationItem } from './NotificationDropdown';
import { OrderStatusTag } from '@/components/Tags';
import { dateFormator } from '@/utils/dateUtils';

const { Text } = Typography;

interface NotificationItemProps {
  notification: NotificationItem;
  onClick: (orderId: NotificationItem['id']) => void;
}
export default function NotificationItem({
  notification,
  onClick,
}: NotificationItemProps) {
  const { t } = useTranslation();

  const color = useMemo(() => {
    switch (notification.status) {
      case 'pending':
        return 'border-blue-400';
      case 'completed':
        return 'border-green-400';
      case 'cancelled':
        return 'border-yellow-400';
      case 'failed':
        return 'border-red-400';
    }
  }, [notification.status]);

  return (
    <Flex
      vertical
      gap={8}
      className={`${color} !-mx-2 rounded-md border-l-4 !px-2 !py-2 transition-colors hover:bg-gray-50`}
    >
      <Flex align='center' justify='space-between' gap={8} className='w-full'>
        <Flex vertical className='ml-4'>
          <Text strong>{notification.memberName}</Text>
          <Text type='secondary'>{notification.description}</Text>
          <Text type='secondary'>
            {dayjs(notification.date).format(dateFormator.accurate)}
          </Text>
        </Flex>
        <OrderStatusTag status={notification.status} />
      </Flex>
      <Button
        key='mark'
        size='small'
        className='w-fit'
        onClick={() => onClick(notification.id)}
      >
        {t('notification.markAsRead')}
      </Button>
    </Flex>
  );
}
