import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { Order } from '@/types';

export default function OrderStatusTag({
  status,
}: {
  status: Order['status'];
}) {
  const { t } = useTranslation();

  const color = useMemo(() => {
    switch (status) {
      case 'pending':
        return 'processing';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'warning';
      case 'failed':
        return 'error';
    }
  }, [status]);
  const icon = useMemo(() => {
    switch (status) {
      case 'pending':
        return <SyncOutlined />;
      case 'completed':
        return <CheckCircleOutlined />;
      case 'cancelled':
        return <ExclamationCircleOutlined />;
      case 'failed':
        return <CloseCircleOutlined />;
    }
  }, [status]);
  const label = useMemo(() => t(status), [t, status]);

  return (
    <Tag color={color} icon={icon} className='!m-0 capitalize'>
      {label}
    </Tag>
  );
}
