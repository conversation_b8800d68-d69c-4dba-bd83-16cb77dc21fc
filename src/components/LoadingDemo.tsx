import React, { useState } from 'react';
import { Card, Button, Row, Col, Typography, Space, Switch } from 'antd';
import { PlayCircleOutlined, StopOutlined } from '@ant-design/icons';
import LoadingPage from './LoadingPage';
import LoadingSpinner from './LoadingSpinner';

const { Title, Text } = Typography;

const LoadingDemo: React.FC = () => {
  const [showFullPageLoading, setShowFullPageLoading] = useState(false);
  const [showOverlay, setShowOverlay] = useState(true);

  return (
    <div className="space-y-6 p-6">
      <div className="text-center mb-8">
        <Title level={2}>Loading Components Demo</Title>
        <Text className="text-gray-600">
          Beautiful and modern loading animations for Galaxy Admin
        </Text>
      </div>

      {/* Full Page Loading Demo */}
      <Card title="Full Page Loading" className="rounded-lg">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setShowFullPageLoading(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Show Full Page Loading
            </Button>
            <div className="flex items-center gap-2">
              <Text>Overlay Mode:</Text>
              <Switch 
                checked={showOverlay} 
                onChange={setShowOverlay}
                size="small"
              />
            </div>
          </div>
          
          <Text className="text-gray-600 block">
            Full screen loading overlay with animated background, logo, and progress indicators.
          </Text>
        </div>
      </Card>

      {/* Loading Spinner Variants */}
      <Card title="Loading Spinner Variants" className="rounded-lg">
        <Row gutter={[24, 24]}>
          {/* Default Variant */}
          <Col xs={24} lg={8}>
            <Card size="small" title="Default Variant" className="h-full">
              <LoadingSpinner 
                size="default" 
                tip="Loading data..." 
                variant="default"
                className="h-40"
              />
            </Card>
          </Col>

          {/* Minimal Variant */}
          <Col xs={24} lg={8}>
            <Card size="small" title="Minimal Variant" className="h-full">
              <LoadingSpinner 
                size="default" 
                tip="Processing..." 
                variant="minimal"
                className="h-40"
              />
            </Card>
          </Col>

          {/* Dots Variant */}
          <Col xs={24} lg={8}>
            <Card size="small" title="Dots Variant" className="h-full">
              <LoadingSpinner 
                size="default" 
                tip="Please wait..." 
                variant="dots"
                className="h-40"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* Size Variations */}
      <Card title="Size Variations" className="rounded-lg">
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={8}>
            <Card size="small" title="Small Size" className="h-full">
              <LoadingSpinner 
                size="small" 
                tip="Loading..." 
                variant="default"
                className="h-32"
              />
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card size="small" title="Default Size" className="h-full">
              <LoadingSpinner 
                size="default" 
                tip="Loading..." 
                variant="default"
                className="h-40"
              />
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card size="small" title="Large Size" className="h-full">
              <LoadingSpinner 
                size="large" 
                tip="Loading..." 
                variant="default"
                className="h-48"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* Without Text */}
      <Card title="Without Text" className="rounded-lg">
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={8}>
            <Card size="small" title="Default (No Text)" className="h-full">
              <LoadingSpinner 
                size="default" 
                variant="default"
                showText={false}
                className="h-32"
              />
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card size="small" title="Minimal (No Text)" className="h-full">
              <LoadingSpinner 
                size="default" 
                variant="minimal"
                showText={false}
                className="h-32"
              />
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card size="small" title="Dots (No Text)" className="h-full">
              <LoadingSpinner 
                size="default" 
                variant="dots"
                showText={false}
                className="h-32"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* Usage Examples */}
      <Card title="Usage Examples" className="rounded-lg">
        <div className="space-y-4">
          <div>
            <Title level={5}>Full Page Loading</Title>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`// Full page loading overlay
<LoadingPage 
  tip="Loading application..." 
  size="large" 
  showLogo={true}
  overlay={true}
/>

// Simple page loading
<LoadingPage 
  tip="Loading page..." 
  size="default" 
  overlay={false}
/>`}
            </pre>
          </div>

          <div>
            <Title level={5}>Component Loading</Title>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`// Default spinner
<LoadingSpinner size="default" tip="Loading data..." />

// Minimal spinner
<LoadingSpinner variant="minimal" showText={false} />

// Dots animation
<LoadingSpinner variant="dots" size="small" tip="Processing..." />`}
            </pre>
          </div>
        </div>
      </Card>

      {/* Full Page Loading Modal */}
      {showFullPageLoading && (
        <LoadingPage
          tip="Demo loading in progress..."
          size="large"
          showLogo={true}
          overlay={showOverlay}
        />
      )}

      {/* Auto-hide demo loading after 3 seconds */}
      {showFullPageLoading && (
        <div style={{ display: 'none' }}>
          {setTimeout(() => setShowFullPageLoading(false), 3000)}
        </div>
      )}
    </div>
  );
};

export default LoadingDemo;
