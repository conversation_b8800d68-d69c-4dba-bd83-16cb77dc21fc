import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import {
  fetchRoles,
  fetchRoleById,
  createRole,
  updateRole,
  deleteRole,
  updateRoleStatus,
  fetchPermissions,
  fetchPermissionCategories,
  fetchUserRoles,
  assignUserRole,
  removeUserRole,
} from '@/pages/Management/apis/rolesApis.ts';
import { queryKeys } from '@/constants/queryKeys.ts';
import type { CreateRoleRequest, UpdateRoleRequest, Role } from '@/types';

// Role Hooks
export const useRoles = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.roles.list(params),
    queryFn: () => fetchRoles(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRole = (id: string) => {
  return useQuery({
    queryKey: queryKeys.roles.detail(id),
    queryFn: () => fetchRoleById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (roleData: CreateRoleRequest) => createRole(roleData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role created successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to create role');
    },
  });
};

export const useUpdateRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRoleRequest }) =>
      updateRole(id, data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role updated successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update role');
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteRole(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role deleted successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to delete role');
    },
  });
};

export const useUpdateRoleStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Role['status'] }) =>
      updateRoleStatus(id, status),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role status updated successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update role status');
    },
  });
};

// Permission Hooks
export const usePermissions = () => {
  return useQuery({
    queryKey: queryKeys.permissions.all,
    queryFn: fetchPermissions,
    staleTime: 10 * 60 * 1000, // 10 minutes (permissions don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const usePermissionCategories = () => {
  return useQuery({
    queryKey: queryKeys.permissions.categories,
    queryFn: fetchPermissionCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// User Role Assignment Hooks
export const useUserRoles = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  roleId?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.userRoles.list(params),
    queryFn: () => fetchUserRoles(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAssignUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, roleId }: { userId: string; roleId: string }) =>
      assignUserRole(userId, roleId),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.userRoles.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role assigned successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to assign role');
    },
  });
};

export const useRemoveUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userRoleId: string) => removeUserRole(userRoleId),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.userRoles.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.roles.all });
      message.success(response.message || 'Role removed successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to remove role');
    },
  });
};
