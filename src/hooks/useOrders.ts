import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import {
  fetchOrders,
  fetchOrderById,
  updateOrderStatus,
} from '@/pages/Orders/apis/ordersApis.ts';
import { queryKeys } from '@/constants/queryKeys.ts';
import type { Order } from '@/types';

export const useOrders = (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  memberId?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.orders.list(params),
    queryFn: () => fetchOrders(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useOrder = (id: string) => {
  return useQuery({
    queryKey: queryKeys.orders.detail(id),
    queryFn: () => fetchOrderById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Order['status'] }) =>
      updateOrderStatus(id, status),
    onSuccess: (response) => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });

      // Show success message
      message.success(response.message || 'Order status updated successfully');
    },
    onError: (error: Error) => {
      // Show error message
      message.error(error.message || 'Failed to update order status');
    },
  });
};
