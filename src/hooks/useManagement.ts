import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import {
  fetchManagementStats,
  fetchEmployees,
  fetchEmployeeById,
  createEmployee,
  updateEmployeeStatus,
  updateEmployee,
  fetchCashRegisters,
  openCashRegister,
  closeCashRegister,
  fetchTransactions,
  createTransaction,
  fetchStores,
  createStore,
  updateStoreStatus,
  fetchProducts,
  updateProductStock,
  fetchShifts,
  endShift,
  getExchangeRates,
  updateExchangeRate,
} from '@/pages/Management/apis/managementApis.ts';
import { queryKeys } from '@/constants/queryKeys.ts';
import type {
  ApiResponse,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  CreateStoreRequest,
  Employee,
  ExchangeRate,
  Store,
  Transaction,
} from '@/types';

// Management Stats Hook
export const useManagementStats = () => {
  return useQuery({
    queryKey: queryKeys.management.stats,
    queryFn: fetchManagementStats,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Employee Hooks
export const useEmployees = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: string;
  department?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.management.employees.list(params),
    queryFn: () => fetchEmployees(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useEmployee = (id: string) => {
  return useQuery({
    queryKey: queryKeys.management.employees.detail(id),
    queryFn: () => fetchEmployeeById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (employeeData: CreateEmployeeRequest) =>
      createEmployee(employeeData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.employees.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Employee created successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to create employee');
    },
  });
};

export const useUpdateEmployeeStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Employee['status'] }) =>
      updateEmployeeStatus(id, status),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.employees.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(
        response.message || 'Employee status updated successfully',
      );
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update employee status');
    },
  });
};

export const useUpdateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmployeeRequest }) =>
      updateEmployee(id, data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.employees.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Employee updated successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update employee');
    },
  });
};

// Cash Register Hooks
export const useCashRegisters = (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  location?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.management.registers.list(params),
    queryFn: () => fetchCashRegisters(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useOpenCashRegister = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      registerId,
      employeeId,
      openingBalance,
    }: {
      registerId: string;
      employeeId: string;
      openingBalance: number;
    }) => openCashRegister(registerId, employeeId, openingBalance),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.registers.all,
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.shifts.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Cash register opened successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to open cash register');
    },
  });
};

export const useCloseCashRegister = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      registerId,
      closingBalance,
    }: {
      registerId: string;
      closingBalance: number;
    }) => closeCashRegister(registerId, closingBalance),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.registers.all,
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.shifts.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Cash register closed successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to close cash register');
    },
  });
};

// Transaction Hooks
export const useTransactions = (params?: {
  page?: number;
  pageSize?: number;
  registerId?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.management.transactions.list(params),
    queryFn: () => fetchTransactions(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateTransaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (transactionData: Omit<Transaction, 'id' | 'timestamp'>) =>
      createTransaction(transactionData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.transactions.all,
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.registers.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Transaction created successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to create transaction');
    },
  });
};

// Store Hooks
export const useStores = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  location?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.management.stores.list(params),
    queryFn: () => fetchStores(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateStore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (storeData: CreateStoreRequest) => createStore(storeData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.stores.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Store created successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to create store');
    },
  });
};

export const useUpdateStoreStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Store['status'] }) =>
      updateStoreStatus(id, status),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.stores.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Store status updated successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update store status');
    },
  });
};

// Product Hooks
export const useProducts = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  category?: string;
  storeId?: string;
  lowStock?: boolean;
}) => {
  return useQuery({
    queryKey: queryKeys.management.products.list(params),
    queryFn: () => fetchProducts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateProductStock = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      newStockLevel,
    }: {
      id: string;
      newStockLevel: number;
    }) => updateProductStock(id, newStockLevel),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.products.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Product stock updated successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to update product stock');
    },
  });
};

// Shift Hooks
export const useShifts = (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  employeeId?: string;
  registerId?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.management.shifts.list(params),
    queryFn: () => fetchShifts(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useEndShift = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      closingBalance,
    }: {
      id: string;
      closingBalance: number;
    }) => endShift(id, closingBalance),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.shifts.all,
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.management.registers.all,
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.management.stats });
      message.success(response.message || 'Shift ended successfully');
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to end shift');
    },
  });
};

// Exchange Rates hooks
export const useExchangeRates = (params?: {
  fiatCode?: string;
  orderBy?: 'spotPrice' | 'askPrice' | 'bidPrice';
  orderByDescending?: boolean;
}) => {
  return useQuery({
    queryKey: queryKeys.management.rates.all(params),
    queryFn: () => getExchangeRates(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateExchangeRate = ({
  onSuccess,
  onError,
}: {
  onSuccess?: (
    response: ApiResponse<ExchangeRate>,
  ) => Promise<unknown> | unknown | undefined;

  onError?: (error: Error) => Promise<unknown> | unknown | undefined;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ exchangeRate }: { exchangeRate: ExchangeRate }) =>
      updateExchangeRate({ exchangeRate }),
    onSuccess: (response) => {
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'management' && query.queryKey[1] === 'rates',
      });
      queryClient.refetchQueries({
        predicate: (query) =>
          query.queryKey[0] === 'management' && query.queryKey[1] === 'rates',
      });
      if (onSuccess) onSuccess(response);
    },
    onError: (error: Error) => {
      if (onError) onError(error);
    },
  });
};
