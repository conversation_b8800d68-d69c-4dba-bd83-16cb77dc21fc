@import 'tailwindcss';

/* Custom CSS Variables for Galaxy Admin */
:root {
  --primary: #1890ff;
  --primary-dark: #096dd9;
  --primary-light: #40a9ff;
  --success: #52c41a;
  --warning: #faad14;
  --error: #f5222d;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --slate-800: #1e293b;
  --slate-900: #0f172a;
}

/* Custom component styles */
.shadow-card {
  box-shadow:
    0 1px 2px 0 rgba(0, 0, 0, 0.03),
    0 1px 6px -1px rgba(0, 0, 0, 0.02),
    0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

.bg-primary {
  background-color: var(--primary);
}

.text-primary {
  color: var(--primary);
}
.permission-panel .ant-collapse-expand-icon {
  position: relative !important;
  top: 12px;
}
.permission-selection-collapse .ant-collapse-expand-icon {
  position: relative !important;
  top: 9px;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.6);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-ripple {
  animation: ripple 1.5s ease-out infinite;
}

/* Glass-morphism effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* Enhanced backdrop blur */
.backdrop-blur-strong {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Smooth transitions for loading states */
.loading-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.border-primary {
  border-color: var(--primary);
}

.hover\:border-primary:hover {
  border-color: var(--primary);
}

.focus\:border-primary:focus {
  border-color: var(--primary);
}

.bg-primary\/10 {
  background-color: rgba(24, 144, 255, 0.1);
}

.hover\:bg-primary\/10:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.from-primary {
  --tw-gradient-from: var(--primary);
}

.to-primary-dark {
  --tw-gradient-to: var(--primary-dark);
}

/* Ant Design Menu customization */
.ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.1) !important;
  border-right: 2px solid var(--primary) !important;
}

.ant-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Dark sidebar styles */
.sidebar-dark {
  background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
}

.sidebar-dark .ant-menu {
  background: transparent !important;
  color: white;
}

.sidebar-dark .ant-menu-item {
  color: rgba(255, 255, 255, 0.8) !important;
  margin: 4px 12px;
  border-radius: 8px;
}

.sidebar-dark .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.sidebar-dark .ant-menu-item-selected {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: white !important;
  border-right: none !important;
}

.sidebar-dark .ant-menu-item-selected::after {
  display: none;
}

/* Hide scrollbar background (track) while keeping scrollability */
.no-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.no-scrollbar::-webkit-scrollbar-track {
  background: transparent; /* This hides the track */
}

.no-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15); /* Optional: scrollbar thumb */
  border-radius: 4px;
}

.no-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent; /* For Firefox */
}

/* Responsive Avatar Styles */
@media (max-width: 640px) {
  .ant-avatar {
    width: 28px !important;
    height: 28px !important;
    line-height: 28px !important;
    font-size: 12px !important;
  }

  .ant-avatar .anticon {
    font-size: 14px !important;
  }
}

/* Responsive List Item Spacing */
@media (max-width: 640px) {
  .ant-list-item-meta-avatar {
    margin-right: 12px !important;
  }

  .ant-list-item-meta-content {
    min-width: 0;
  }
}

