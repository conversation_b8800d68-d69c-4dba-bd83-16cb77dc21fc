import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  Avatar,
  Upload,
  Space,
  message,
  Select,
  DatePicker
} from 'antd';
import {
  SaveOutlined,
  UserOutlined,
  UploadOutlined,
  CameraOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  dateOfBirth: dayjs.Dayjs;
  address: string;
  bio: string;
  avatar: string;
}

const ProfileSettings: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string>('');

  // Mock initial data - in real app this would come from API
  const initialData: ProfileData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '******-0123',
    position: 'System Administrator',
    department: 'IT Department',
    dateOfBirth: dayjs('1990-01-15'),
    address: '123 Admin Street, Las Vegas, NV 89101',
    bio: 'Experienced system administrator with 5+ years in casino management systems.',
    avatar: '',
  };
  //@ts-expect-error
  const handleSave = async (values: ProfileData) => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.profile.saveSuccess'));
    } catch (error) {
      message.error(t('settings.profile.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world
      setAvatarUrl(info.file.response?.url || '');
      message.success(t('settings.profile.avatarUploadSuccess'));
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error(t('settings.profile.avatarFormatError'));
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error(t('settings.profile.avatarSizeError'));
    }
    return isJpgOrPng && isLt2M;
  };

  return (
    <div className="space-y-6">
      <Form
        form={form}
        layout="vertical"
        initialValues={initialData}
        onFinish={handleSave}
        className="space-y-6"
      >
        {/* Profile Picture */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <CameraOutlined className="text-blue-600" />
              {t('settings.profile.profilePicture')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="flex flex-col sm:flex-row items-center gap-6">
            <Avatar
              size={120}
              src={avatarUrl}
              icon={<UserOutlined />}
              className="bg-gradient-to-br from-blue-500 to-purple-600"
            />
            <div className="flex-1">
              <Title level={4} className="mb-2">
                {t('settings.profile.changeAvatar')}
              </Title>
              <Text className="text-gray-500 block mb-4">
                {t('settings.profile.avatarDescription')}
              </Text>
              <Upload
                name="avatar"
                listType="picture"
                className="avatar-uploader"
                showUploadList={false}
                action="/api/upload"
                beforeUpload={beforeUpload}
                onChange={handleAvatarChange}
              >
                <Button icon={<UploadOutlined />} size="large">
                  {t('settings.profile.uploadAvatar')}
                </Button>
              </Upload>
            </div>
          </div>
        </Card>

        {/* Personal Information */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <UserOutlined className="text-blue-600" />
              {t('settings.profile.personalInfo')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name="firstName"
                label={t('settings.profile.firstName')}
                rules={[{ required: true, message: t('settings.profile.firstNameRequired') }]}
              >
                <Input size="large" prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="lastName"
                label={t('settings.profile.lastName')}
                rules={[{ required: true, message: t('settings.profile.lastNameRequired') }]}
              >
                <Input size="large" prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="email"
                label={t('settings.profile.email')}
                rules={[
                  { required: true, message: t('settings.profile.emailRequired') },
                  { type: 'email', message: t('settings.profile.emailInvalid') }
                ]}
              >
                <Input size="large" prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="phone"
                label={t('settings.profile.phone')}
                rules={[{ required: true, message: t('settings.profile.phoneRequired') }]}
              >
                <Input size="large" prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="dateOfBirth"
                label={t('settings.profile.dateOfBirth')}
              >
                <DatePicker 
                  size="large" 
                  className="w-full"
                  format="YYYY-MM-DD"
                  defaultValue={dayjs('1990-01-15', 'YYYY-MM-DD')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="position"
                label={t('settings.profile.position')}
                rules={[{ required: true, message: t('settings.profile.positionRequired') }]}
              >
                <Select size="large" placeholder={t('settings.profile.selectPosition')}>
                  <Option value="System Administrator">System Administrator</Option>
                  <Option value="Manager">Manager</Option>
                  <Option value="Supervisor">Supervisor</Option>
                  <Option value="Analyst">Analyst</Option>
                  <Option value="Operator">Operator</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="address"
                label={t('settings.profile.address')}
              >
                <TextArea 
                  rows={3} 
                  size="large"
                  placeholder={t('settings.profile.addressPlaceholder')}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="bio"
                label={t('settings.profile.bio')}
              >
                <TextArea 
                  rows={4} 
                  size="large"
                  placeholder={t('settings.profile.bioPlaceholder')}
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Work Information */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <HomeOutlined className="text-blue-600" />
              {t('settings.profile.workInfo')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name="department"
                label={t('settings.profile.department')}
                rules={[{ required: true, message: t('settings.profile.departmentRequired') }]}
              >
                <Select size="large" placeholder={t('settings.profile.selectDepartment')}>
                  <Option value="IT Department">IT Department</Option>
                  <Option value="Management">Management</Option>
                  <Option value="Gaming Operations">Gaming Operations</Option>
                  <Option value="Security">Security</Option>
                  <Option value="Customer Service">Customer Service</Option>
                  <Option value="Finance">Finance</Option>
                  <Option value="Human Resources">Human Resources</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                label={t('settings.profile.employeeId')}
              >
                <Input 
                  size="large" 
                  value="EMP001" 
                  disabled 
                  className="bg-gray-50"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Account Statistics */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <CalendarOutlined className="text-blue-600" />
              {t('settings.profile.accountStats')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={8}>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">2</div>
                <div className="text-sm text-blue-700">{t('settings.profile.yearsActive')}</div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">1,247</div>
                <div className="text-sm text-green-700">{t('settings.profile.loginSessions')}</div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">98.5%</div>
                <div className="text-sm text-purple-700">{t('settings.profile.uptime')}</div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Action Buttons */}
        <Card className="rounded-lg">
          <div className="flex justify-end">
            <Space>
              <Button size="large">
                {t('common.cancel')}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('settings.profile.saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default ProfileSettings;
