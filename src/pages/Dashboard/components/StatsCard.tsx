import React from 'react';
import { Card, Typography } from 'antd';
import { formatCurrency } from '@/utils/tableUtils.ts';

const { Text } = Typography;

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  isCurrency?: boolean;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  isCurrency = false,
}) => {
  const formattedValue = isCurrency ? formatCurrency(value) : value;

  return (
    <Card
      className='shadow-card hover:shadow-lg transition-all duration-300 border-0 rounded-2xl overflow-hidden group hover:scale-105'
      bodyStyle={{ padding: '24px' }}
    >
      <div className='relative'>
        {/* Background decoration */}
        <div
          className='absolute -top-2 -right-2 w-20 h-20 rounded-full opacity-10 group-hover:opacity-20 transition-opacity'
          style={{ backgroundColor: color }}
        />

        <div className='flex items-start justify-between relative z-10'>
          <div className='flex-1'>
            <Text className='text-sm font-medium text-gray-500 mb-2 block uppercase tracking-wide'>
              {title}
            </Text>
            <div className='mb-3'>
              <span
                className='text-3xl font-bold leading-none'
                style={{ color }}
              >
                {formattedValue}
              </span>
            </div>
          </div>

          <div className='flex-shrink-0'>
            <div
              className='w-14 h-14 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-shadow'
              style={{
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
              }}
            >
              <div className='text-2xl'>{icon}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StatsCard;
