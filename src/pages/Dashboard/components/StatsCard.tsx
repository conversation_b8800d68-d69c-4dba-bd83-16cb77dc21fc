import React from 'react';
import { Card, Typography } from 'antd';
import { formatCurrency } from '@/utils/tableUtils.ts';

const { Text } = Typography;

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  isCurrency?: boolean;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  isCurrency = false,
}) => {
  const formattedValue = isCurrency ? formatCurrency(value) : value;

  return (
    <Card
      className='shadow-card hover:shadow-lg transition-all duration-300 border-0 rounded-xl sm:rounded-2xl overflow-hidden group hover:scale-105'
      bodyStyle={{ padding: '16px' }}
    >
      <div className='relative'>
        {/* Background decoration */}
        <div
          className='absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-12 h-12 sm:w-20 sm:h-20 rounded-full opacity-10 group-hover:opacity-20 transition-opacity'
          style={{ backgroundColor: color }}
        />

        <div className='flex items-start justify-between relative z-10'>
          <div className='flex-1 min-w-0'>
            <Text className='text-xs sm:text-sm font-medium text-gray-500 mb-1 sm:mb-2 block uppercase tracking-wide truncate'>
              {title}
            </Text>
            <div className='mb-2 sm:mb-3'>
              <span
                className='text-xl sm:text-2xl lg:text-3xl font-bold leading-none'
                style={{ color }}
              >
                {formattedValue}
              </span>
            </div>
          </div>

          <div className='flex-shrink-0 ml-2 sm:ml-3'>
            <div
              className='w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-shadow'
              style={{
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
              }}
            >
              <div className='text-lg sm:text-xl lg:text-2xl'>{icon}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StatsCard;
