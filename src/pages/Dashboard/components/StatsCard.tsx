import React from 'react';
import { Card, Typography } from 'antd';
import { formatCurrency } from '@/utils/tableUtils.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';

const { Text } = Typography;

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  isCurrency?: boolean;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  isCurrency = false,
}) => {
  const formattedValue = isCurrency ? formatCurrency(value) : value;
  const { isMobile, isTablet } = useResponsive();

  // Responsive values
  const cardPadding = useResponsiveValue({
    xs: '12px',
    sm: '16px',
    md: '20px',
    lg: '24px',
    xl: '24px',
    '2xl': '24px'
  });

  const iconSize = useResponsiveValue({
    xs: { width: '32px', height: '32px' },
    sm: { width: '40px', height: '40px' },
    md: { width: '48px', height: '48px' },
    lg: { width: '56px', height: '56px' },
    xl: { width: '56px', height: '56px' },
    '2xl': { width: '56px', height: '56px' }
  });

  const decorationSize = useResponsiveValue({
    xs: { width: '40px', height: '40px' },
    sm: { width: '60px', height: '60px' },
    md: { width: '80px', height: '80px' },
    lg: { width: '80px', height: '80px' },
    xl: { width: '80px', height: '80px' },
    '2xl': { width: '80px', height: '80px' }
  });

  return (
    <Card
      className={`shadow-card hover:shadow-lg transition-all duration-300 border-0 ${isMobile ? 'rounded-xl' : 'rounded-xl sm:rounded-2xl'} overflow-hidden group hover:scale-105`}
      bodyStyle={{ padding: cardPadding }}
    >
      <div className='relative'>
        {/* Background decoration */}
        <div
          className={`absolute ${isMobile ? '-top-1 -right-1' : '-top-1 -right-1 sm:-top-2 sm:-right-2'} rounded-full opacity-10 group-hover:opacity-20 transition-opacity`}
          style={{
            backgroundColor: color,
            width: decorationSize?.width,
            height: decorationSize?.height
          }}
        />

        <div className='flex items-start justify-between relative z-10'>
          <div className='flex-1 min-w-0'>
            <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} font-medium text-gray-500 ${isMobile ? 'mb-1' : 'mb-1 sm:mb-2'} block uppercase tracking-wide truncate`}>
              {title}
            </Text>
            <div className={`${isMobile ? 'mb-2' : 'mb-2 sm:mb-3'}`}>
              <span
                className={`${isMobile ? 'text-lg' : 'text-xl sm:text-2xl lg:text-3xl'} font-bold leading-none`}
                style={{ color }}
              >
                {formattedValue}
              </span>
            </div>
          </div>

          <div className={`flex-shrink-0 ${isMobile ? 'ml-2' : 'ml-2 sm:ml-3'}`}>
            <div
              className={`${isMobile ? 'rounded-lg' : 'rounded-lg sm:rounded-xl'} flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-shadow`}
              style={{
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
                width: iconSize?.width,
                height: iconSize?.height
              }}
            >
              <div className={`${isMobile ? 'text-base' : 'text-lg sm:text-xl lg:text-2xl'}`}>{icon}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StatsCard;
