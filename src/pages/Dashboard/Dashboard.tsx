import React from 'react';
import {
  Row,
  Col,
  Spin,
  Alert,
  Typography,
  Card,
  List,
  Avatar,
  Tag,
} from 'antd';
import {
  UserOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDashboardStats } from '@/hooks/useDashboard.ts';
import { useOrders } from '@/hooks/useOrders.ts';
import { useMembers } from '@/hooks/useMembers.ts';
import StatsCard from '@/pages/Dashboard/components/StatsCard';
import { formatDate, formatCurrency } from '@/utils/tableUtils.ts';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { data: statsResponse, isLoading, error } = useDashboardStats();
  const { data: recentOrdersResponse } = useOrders({ page: 1, pageSize: 5 });
  const { data: recentMembersResponse } = useMembers({ page: 1, pageSize: 5 });

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-64'>
        <Spin size='large' />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load dashboard statistics'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const stats = statsResponse?.data;
  const recentOrders = recentOrdersResponse?.data?.data || [];
  const recentMembers = recentMembersResponse?.data?.data || [];

  if (!stats) {
    return (
      <Alert
        message='No Data'
        description='No dashboard statistics available'
        type='info'
        showIcon
        className='rounded-lg'
      />
    );
  }

  return (
    <div className='space-y-4 sm:space-y-6 lg:space-y-8'>
      {/* Welcome Section */}
      <div className='bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 border border-primary/10'>
        <div className='flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-4'>
          <div className='w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-primary to-primary-dark rounded-lg sm:rounded-xl flex items-center justify-center flex-shrink-0'>
            <TrophyOutlined className='text-white text-lg sm:text-xl' />
          </div>
          <div className='flex-1 min-w-0'>
            <Title level={2} className='text-gray-900 mb-1 font-bold text-lg sm:text-xl lg:text-2xl'>
              {t('dashboard.welcome')}
            </Title>
            <Text className='text-gray-600 text-sm sm:text-base leading-relaxed'>
              Monitor your casino operations and track key performance metrics
            </Text>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.totalMembers')}
            value={stats.totalMembers}
            icon={<UserOutlined />}
            color='#1890ff'
            trend={{ value: 12, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.recentOrders')}
            value={stats.recentOrders}
            icon={<ShoppingCartOutlined />}
            color='#52c41a'
            trend={{ value: 8, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.pendingTransactions')}
            value={stats.pendingTransactions}
            icon={<ClockCircleOutlined />}
            color='#faad14'
            trend={{ value: 3, isPositive: false }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.totalRevenue')}
            value={stats.totalRevenue}
            icon={<DollarOutlined />}
            color='#722ed1'
            isCurrency
            trend={{ value: 15, isPositive: true }}
          />
        </Col>
      </Row>

      {/* Recent Activity */}
      <Row gutter={[24, 24]}>
        {/* Recent Orders */}
        <Col xs={24} lg={12}>
          <Card
            className='rounded-2xl border-0 shadow-card h-full'
            title={
              <div className='flex py-4 items-center gap-4'>
                <div className='w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center'>
                  <ShoppingCartOutlined className='text-green-600' />
                </div>
                <div className='flex flex-col justify-center'>
                  <Title
                    level={4}
                    style={{ margin: 0 }}
                    className='text-gray-900'
                  >
                    Recent Orders
                  </Title>
                  <Text className='text-sm text-gray-500'>
                    Latest transactions
                  </Text>
                </div>
              </div>
            }
            bodyStyle={{ padding: '0 24px 24px 24px' }}
          >
            <List
              dataSource={recentOrders}
              renderItem={(order) => (
                <List.Item className='border-0 px-0 py-3'>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        className='bg-primary/10 text-primary'
                        icon={<ShoppingCartOutlined />}
                      />
                    }
                    title={
                      <div className='flex items-center justify-between'>
                        <Text className='font-medium text-gray-900'>
                          {order.memberName}
                        </Text>
                        <Text className='font-semibold text-gray-900'>
                          {formatCurrency(order.amount)}
                        </Text>
                      </div>
                    }
                    description={
                      <div className='flex items-center justify-between mt-1'>
                        <Text className='text-sm text-gray-500'>
                          {formatDate(order.date)}
                        </Text>
                        <Tag
                          color={
                            order.status === 'completed'
                              ? 'green'
                              : order.status === 'pending'
                                ? 'orange'
                                : 'red'
                          }
                          className='rounded-full px-2'
                        >
                          {order.status}
                        </Tag>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Recent Members */}
        <Col xs={24} lg={12}>
          <Card
            className='rounded-2xl border-0 shadow-card h-full'
            title={
              <div className='flex py-4 items-center gap-4'>
                <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
                  <UserOutlined className='text-blue-600' />
                </div>
                <div>
                  <Title
                    level={4}
                    style={{ margin: 0 }}
                    className='m-0 text-gray-900'
                  >
                    New Members
                  </Title>
                  <Text className='text-sm text-gray-500'>
                    Recently registered
                  </Text>
                </div>
              </div>
            }
            bodyStyle={{ padding: '0 24px 24px 24px' }}
          >
            <List
              dataSource={recentMembers}
              renderItem={(member) => (
                <List.Item className='border-0 px-0 py-3'>
                  <List.Item.Meta
                    avatar={
                      <Avatar className='bg-gradient-to-br from-primary to-primary-dark text-white font-medium'>
                        {member.name.charAt(0).toUpperCase()}
                      </Avatar>
                    }
                    title={
                      <div className='flex items-center justify-between'>
                        <Text className='font-medium text-gray-900'>
                          {member.name}
                        </Text>
                        <Tag
                          color={member.status === 'active' ? 'green' : 'red'}
                          className='rounded-full px-2'
                        >
                          {member.status}
                        </Tag>
                      </div>
                    }
                    description={
                      <div className='flex items-center justify-between mt-1'>
                        <Text className='text-sm text-gray-500'>
                          {member.passport}
                        </Text>
                        <Text className='text-sm text-gray-500'>
                          {formatDate(member.registrationDate)}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
