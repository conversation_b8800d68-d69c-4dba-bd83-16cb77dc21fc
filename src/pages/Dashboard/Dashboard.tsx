import React from 'react';
import {
  Row,
  Col,
  Spin,
  Alert,
  Typo<PERSON>,
  Card,
  List,
  Avatar,
  Tag,
} from 'antd';
import {
  UserOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDashboardStats } from '@/hooks/useDashboard.ts';
import { useOrders } from '@/hooks/useOrders.ts';
import { useMembers } from '@/hooks/useMembers.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import StatsCard from '@/pages/Dashboard/components/StatsCard';
import { formatDate, formatCurrency } from '@/utils/tableUtils.ts';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { data: statsResponse, isLoading, error } = useDashboardStats();
  const { data: recentOrdersResponse } = useOrders({ page: 1, pageSize: 5 });
  const { data: recentMembersResponse } = useMembers({ page: 1, pageSize: 5 });

  // Responsive hooks
  const { isMobile, isTablet } = useResponsive();

  // Responsive values
  const avatarSize = useResponsiveValue({
    xs: 'small',
    sm: 'small',
    md: 'default',
    lg: 'default',
    xl: 'default',
    '2xl': 'default'
  }) as 'small' | 'default';

  const cardBodyStyle = useResponsiveValue({
    xs: { padding: '12px 16px' },
    sm: { padding: '16px 20px' },
    md: { padding: '20px 24px' },
    lg: { padding: '24px' },
    xl: { padding: '24px' },
    '2xl': { padding: '24px' }
  });

  const cardHeadStyle = useResponsiveValue({
    xs: { padding: '12px 16px' },
    sm: { padding: '16px 20px' },
    md: { padding: '16px 24px' },
    lg: { padding: '16px 24px' },
    xl: { padding: '16px 24px' },
    '2xl': { padding: '16px 24px' }
  });

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-64'>
        <Spin size='large' />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load dashboard statistics'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const stats = statsResponse?.data;
  const recentOrders = recentOrdersResponse?.data?.data || [];
  const recentMembers = recentMembersResponse?.data?.data || [];

  if (!stats) {
    return (
      <Alert
        message='No Data'
        description='No dashboard statistics available'
        type='info'
        showIcon
        className='rounded-lg'
      />
    );
  }

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}>
      {/* Welcome Section */}
      <div className={`bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-xl ${!isMobile ? 'sm:rounded-2xl' : ''} ${isMobile ? 'p-4' : isTablet ? 'p-6' : 'p-8'} border border-primary/10`}>
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} items-start sm:items-center ${isMobile ? 'gap-3' : 'gap-3 sm:gap-4'} mb-4`}>
          <div className={`${isMobile ? 'w-10 h-10' : 'w-10 h-10 sm:w-12 sm:h-12'} bg-gradient-to-br from-primary to-primary-dark ${isMobile ? 'rounded-lg' : 'rounded-lg sm:rounded-xl'} flex items-center justify-center flex-shrink-0`}>
            <TrophyOutlined className={`text-white ${isMobile ? 'text-lg' : 'text-lg sm:text-xl'}`} />
          </div>
          <div className='flex-1 min-w-0'>
            <Title level={2} className={`text-gray-900 mb-1 font-bold ${isMobile ? 'text-lg' : 'text-lg sm:text-xl lg:text-2xl'}`}>
              {t('dashboard.welcome')}
            </Title>
            <Text className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'} leading-relaxed`}>
              Monitor your casino operations and track key performance metrics
            </Text>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <Row gutter={[{ xs: 12, sm: 16, lg: 24 }, { xs: 12, sm: 16, lg: 24 }]}>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.totalMembers')}
            value={stats.totalMembers}
            icon={<UserOutlined />}
            color='#1890ff'
            trend={{ value: 12, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.recentOrders')}
            value={stats.recentOrders}
            icon={<ShoppingCartOutlined />}
            color='#52c41a'
            trend={{ value: 8, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.pendingTransactions')}
            value={stats.pendingTransactions}
            icon={<ClockCircleOutlined />}
            color='#faad14'
            trend={{ value: 3, isPositive: false }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('dashboard.stats.totalRevenue')}
            value={stats.totalRevenue}
            icon={<DollarOutlined />}
            color='#722ed1'
            isCurrency
            trend={{ value: 15, isPositive: true }}
          />
        </Col>
      </Row>

      {/* Recent Activity */}
      <Row gutter={[{ xs: 12, sm: 16, lg: 24 }, { xs: 12, sm: 16, lg: 24 }]}>
        {/* Recent Orders */}
        <Col xs={24} lg={12}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-xl sm:rounded-2xl'} border-0 shadow-card h-full`}
            title={
              <div className={`flex ${isMobile ? 'py-2' : 'py-2 sm:py-4'} items-center ${isMobile ? 'gap-3' : 'gap-3 sm:gap-4'}`}>
                <div className={`${isMobile ? 'w-6 h-6' : 'w-6 h-6 sm:w-8 sm:h-8'} bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0`}>
                  <ShoppingCartOutlined className={`text-green-600 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'}`} />
                </div>
                <div className='flex flex-col justify-center min-w-0 flex-1'>
                  <Title
                    level={4}
                    style={{ margin: 0 }}
                    className={`text-gray-900 ${isMobile ? 'text-base' : 'text-base sm:text-lg'}`}
                  >
                    Recent Orders
                  </Title>
                  <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} text-gray-500 truncate`}>
                    Latest transactions
                  </Text>
                </div>
              </div>
            }
            bodyStyle={cardBodyStyle}
            headStyle={cardHeadStyle}
          >
            <List
              dataSource={recentOrders}
              renderItem={(order) => (
                <List.Item className={`border-0 px-0 ${isMobile ? 'py-2' : 'py-2 sm:py-3'}`}>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        className='bg-primary/10 text-primary flex-shrink-0'
                        icon={<ShoppingCartOutlined />}
                        size={avatarSize}
                      />
                    }
                    title={
                      <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row sm:items-center sm:justify-between'} ${isMobile ? 'gap-1' : 'gap-1 sm:gap-2'}`}>
                        <Text className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'} truncate`}>
                          {order.memberName}
                        </Text>
                        <Text className={`font-semibold text-gray-900 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'}`}>
                          {formatCurrency(order.amount)}
                        </Text>
                      </div>
                    }
                    description={
                      <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row sm:items-center sm:justify-between'} ${isMobile ? 'gap-1' : 'gap-1 sm:gap-2'} mt-1`}>
                        <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} text-gray-500`}>
                          {formatDate(order.date)}
                        </Text>
                        <Tag
                          color={
                            order.status === 'completed'
                              ? 'green'
                              : order.status === 'pending'
                                ? 'orange'
                                : 'red'
                          }
                          className={`rounded-full px-2 text-xs ${isMobile ? 'self-start' : 'self-start sm:self-auto'}`}
                        >
                          {order.status}
                        </Tag>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Recent Members */}
        <Col xs={24} lg={12}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-xl sm:rounded-2xl'} border-0 shadow-card h-full`}
            title={
              <div className={`flex ${isMobile ? 'py-2' : 'py-2 sm:py-4'} items-center ${isMobile ? 'gap-3' : 'gap-3 sm:gap-4'}`}>
                <div className={`${isMobile ? 'w-6 h-6' : 'w-6 h-6 sm:w-8 sm:h-8'} bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0`}>
                  <UserOutlined className={`text-blue-600 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'}`} />
                </div>
                <div className='min-w-0 flex-1'>
                  <Title
                    level={4}
                    style={{ margin: 0 }}
                    className={`m-0 text-gray-900 ${isMobile ? 'text-base' : 'text-base sm:text-lg'}`}
                  >
                    New Members
                  </Title>
                  <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} text-gray-500 truncate`}>
                    Recently registered
                  </Text>
                </div>
              </div>
            }
            bodyStyle={cardBodyStyle}
            headStyle={cardHeadStyle}
          >
            <List
              dataSource={recentMembers}
              renderItem={(member) => (
                <List.Item className={`border-0 px-0 ${isMobile ? 'py-2' : 'py-2 sm:py-3'}`}>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        className='bg-gradient-to-br from-primary to-primary-dark text-white font-medium flex-shrink-0'
                        size={avatarSize}
                      >
                        {member.name.charAt(0).toUpperCase()}
                      </Avatar>
                    }
                    title={
                      <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row sm:items-center sm:justify-between'} ${isMobile ? 'gap-1' : 'gap-1 sm:gap-2'}`}>
                        <Text className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'} truncate`}>
                          {member.name}
                        </Text>
                        <Tag
                          color={member.status === 'active' ? 'green' : 'red'}
                          className={`rounded-full px-2 text-xs ${isMobile ? 'self-start' : 'self-start sm:self-auto'}`}
                        >
                          {member.status}
                        </Tag>
                      </div>
                    }
                    description={
                      <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row sm:items-center sm:justify-between'} ${isMobile ? 'gap-1' : 'gap-1 sm:gap-2'} mt-1`}>
                        <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} text-gray-500 truncate`}>
                          {member.passport}
                        </Text>
                        <Text className={`${isMobile ? 'text-xs' : 'text-xs sm:text-sm'} text-gray-500`}>
                          {formatDate(member.registrationDate)}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
