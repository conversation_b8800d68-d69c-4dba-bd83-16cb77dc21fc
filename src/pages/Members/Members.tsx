import React, { useState } from 'react';
import {
  Button,
  Input,
  Space,
  Tag,
  Alert,
  Card,
  Typography,
  Avatar,
  Statistic,
  Row,
  Col,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useMembers } from '@/hooks/useMembers.ts';
import {
  formatDate,
  getStatusColor,
  formatCurrency,
} from '@/utils/tableUtils.ts';
import GalaxyTable from '@/components/GalaxyTable';
import MemberForm from '@/pages/Members/components/MemberForm';
import MemberDetailsModal from './Modals/MemberDetailsModal';
import type { Member } from '@/types';

const { Search } = Input;
const { Title, Text } = Typography;

const Members: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [showForm, setShowForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // Responsive values
  const searchWidth = useResponsiveValue({
    xs: '100%',
    sm: '100%',
    md: '320px',
    lg: '320px',
    xl: '320px',
    '2xl': '320px'
  });

  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40
  });

  const {
    data: membersResponse,
    isLoading,
    error,
  } = useMembers({
    page: pagination.current,
    pageSize: pagination.pageSize,
    search: searchTerm,
  });

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleViewMember = (member: Member) => {
    setSelectedMember(member);
    setShowDetailsModal(true);
  };

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedMember(null);
  };

  const getMembershipTierColor = (tier?: string) => {
    switch (tier) {
      case 'bronze':
        return '#cd7f32';
      case 'silver':
        return '#c0c0c0';
      case 'gold':
        return '#ffd700';
      case 'platinum':
        return '#e5e4e2';
      default:
        return '#1890ff';
    }
  };

  const columns = [
    {
      title: 'Member',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Member) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            className='bg-gradient-to-br from-primary to-primary-dark text-white font-medium flex-shrink-0'
            size={avatarSize}
          >
            {name.charAt(0).toUpperCase()}
          </Avatar>
          <div className='min-w-0 flex-1'>
            <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
              <Text className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''} truncate`}>{name}</Text>
              {record.membershipTier && !isMobile && (
                <CrownOutlined
                  style={{
                    color: getMembershipTierColor(record.membershipTier),
                  }}
                  className='text-sm flex-shrink-0'
                />
              )}
            </div>
            <Text className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}>ID: {record.id}</Text>
            {record.email && !isMobile && (
              <Text className='text-xs text-gray-400 block truncate'>
                {record.email}
              </Text>
            )}
          </div>
        </div>
      ),
      sorter: (a: Member, b: Member) => a.name.localeCompare(b.name),
      width: isMobile ? 200 : undefined,
    },
    {
      title: t('members.passport'),
      dataIndex: 'passport',
      key: 'passport',
      render: (passport: string) => (
        <div className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} bg-gray-50 ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}>
          {isMobile ? passport.slice(-6) : passport}
        </div>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Membership',
      dataIndex: 'membershipTier',
      key: 'membershipTier',
      render: (tier: string, record: Member) => (
        <div>
          {tier && (
            <Tag
              color={getMembershipTierColor(tier)}
              className={`font-medium mb-1 ${isMobile ? 'text-xs' : ''}`}
            >
              {tier.charAt(0).toUpperCase() + tier.slice(1)}
            </Tag>
          )}
          {!isMobile && (
            <div className='text-xs text-gray-500'>
              {record.totalOrders || 0} orders •{' '}
              {formatCurrency(record.totalSpent || 0)}
            </div>
          )}
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('members.registrationDate'),
      dataIndex: 'registrationDate',
      key: 'registrationDate',
      render: (date: string) => (
        <div>
          <Text className={`text-gray-900 block ${isMobile ? 'text-xs' : ''}`}>
            {formatDate(date).split(',')[0]}
          </Text>
          {!isMobile && (
            <Text className='text-sm text-gray-500'>
              {formatDate(date).split(',')[1]}
            </Text>
          )}
        </div>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('members.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag
          color={getStatusColor(status)}
          className={`rounded-full ${isMobile ? 'px-2 py-1 text-xs' : 'px-3 py-1'} font-medium`}
        >
          {t(`members.${status}`)}
        </Tag>
      ),
      filters: [
        { text: t('members.active'), value: 'active' },
        { text: t('members.inactive'), value: 'inactive' },
      ],
      onFilter: (value: any, record: Member) => record.status === value,
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('members.actions'),
      key: 'actions',
      width: isMobile ? 60 : 120,
      render: (_: any, record: Member) => (
        <Space>
          <Button
            type='text'
            icon={<EyeOutlined />}
            className='hover:bg-primary/10 hover:text-primary rounded-lg'
            onClick={() => handleViewMember(record)}
            size={isMobile ? 'small' : 'middle'}
          >
            {!isMobile && 'View'}
          </Button>
        </Space>
      ),
    },
  ];

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load members'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const members = membersResponse?.data?.data || [];
  const total = membersResponse?.data?.total || 0;
  const activeMembers = members.filter((m) => m.status === 'active').length;
  const inactiveMembers = members.filter((m) => m.status === 'inactive').length;


  const platinumMembers = members.filter(
    (m) => m.membershipTier === 'platinum',
  ).length;
  const goldMembers = members.filter((m) => m.membershipTier === 'gold').length;
  const silverMembers = members.filter(
    (m) => m.membershipTier === 'silver',
  ).length;
  const bronzeMembers = members.filter(
    (m) => m.membershipTier === 'bronze',
  ).length;

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}>
      {/* Header Section */}
      <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 ${isMobile ? 'rounded-xl p-4' : 'rounded-2xl p-6 lg:p-8'} border border-blue-100`}>
        <div className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}>
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center flex-shrink-0`}>
              <TeamOutlined className={`text-white ${isMobile ? 'text-lg' : 'text-xl'}`} />
            </div>
            <div className='min-w-0 flex-1'>
              <Title level={2} className={`text-gray-900 mb-1 font-bold ${isMobile ? 'text-lg' : ''}`}>
                {t('members.title')}
              </Title>
              <Text className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
                Manage casino member registrations and profiles
              </Text>
            </div>
          </div>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => setShowForm(true)}
            size={isMobile ? 'middle' : 'large'}
            className={`rounded-lg ${isMobile ? 'h-10 px-4 w-full' : 'h-12 px-6'} font-medium shadow-lg hover:shadow-xl transition-all`}
          >
            {t('members.addMember')}
          </Button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <Row gutter={[{ xs: 12, sm: 16, lg: 24 }, { xs: 12, sm: 16, lg: 24 }]}>
        <Col xs={24} sm={12} lg={8}>
          <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card text-center border border-blue-100 bg-blue-50/50`}>
            <Statistic
              title='Total Members'
              value={total}
              prefix={<UserOutlined className='text-blue-600' />}
              valueStyle={{
                color: '#1d4ed8',
                fontSize: isMobile ? '20px' : '24px',
                fontWeight: 'bold',
              }}
            />
            <div className='mt-2'>
              <Progress
                percent={
                  total > 0 ? Math.round((activeMembers / total) * 100) : 0
                }
                size='small'
                strokeColor='#1d4ed8'
                showInfo={false}
              />
              <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600 mt-1`}>
                {Math.round((activeMembers / total) * 100)}% active
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card text-center border border-green-100 bg-green-50/50`}>
            <Statistic
              title='Active Members'
              value={activeMembers}
              prefix={<TeamOutlined className='text-green-600' />}
              valueStyle={{
                color: '#059669',
                fontSize: isMobile ? '20px' : '24px',
                fontWeight: 'bold',
              }}
            />
            <div className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-green-600`}>
              {inactiveMembers} inactive
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card text-center border border-orange-100 bg-orange-50/50`}>
            <Statistic
              title='VIP Members'
              value={platinumMembers + goldMembers}
              prefix={<CrownOutlined className='text-orange-600' />}
              valueStyle={{
                color: '#ea580c',
                fontSize: isMobile ? '20px' : '24px',
                fontWeight: 'bold',
              }}
            />
            <div className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-orange-600`}>
              {isMobile ? `${platinumMembers + goldMembers} VIP` : `${platinumMembers} Platinum • ${goldMembers} Gold`}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Membership Tier Breakdown */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <div className='mb-4'>
          <Typography.Title level={5} className='text-gray-900 mb-2'>
            Membership Tier Distribution
          </Typography.Title>
          <Typography.Text className='text-gray-600'>
            Member distribution across different tiers
          </Typography.Text>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <div className='text-center'>
              <div className='text-2xl font-bold' style={{ color: '#e5e4e2' }}>
                {platinumMembers}
              </div>
              <div className='text-sm text-gray-600 flex items-center justify-center gap-1'>
                <CrownOutlined style={{ color: '#e5e4e2' }} />
                Platinum
              </div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className='text-center'>
              <div className='text-2xl font-bold' style={{ color: '#ffd700' }}>
                {goldMembers}
              </div>
              <div className='text-sm text-gray-600 flex items-center justify-center gap-1'>
                <CrownOutlined style={{ color: '#ffd700' }} />
                Gold
              </div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className='text-center'>
              <div className='text-2xl font-bold' style={{ color: '#c0c0c0' }}>
                {silverMembers}
              </div>
              <div className='text-sm text-gray-600 flex items-center justify-center gap-1'>
                <CrownOutlined style={{ color: '#c0c0c0' }} />
                Silver
              </div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className='text-center'>
              <div className='text-2xl font-bold' style={{ color: '#cd7f32' }}>
                {bronzeMembers}
              </div>
              <div className='text-sm text-gray-600 flex items-center justify-center gap-1'>
                <CrownOutlined style={{ color: '#cd7f32' }} />
                Bronze
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Search and Filters */}
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}>
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start sm:items-center justify-between`}>
          <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
            <SearchOutlined className={`text-gray-400 ${isMobile ? 'text-base' : 'text-lg'}`} />
            <div>
              <Text className={`font-medium text-gray-900 block ${isMobile ? 'text-sm' : ''}`}>
                Search Members
              </Text>
              <Text className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>
                Find members by name or passport
              </Text>
            </div>
          </div>
          <Search
            placeholder='Search by name or passport...'
            allowClear
            size={isMobile ? 'middle' : 'large'}
            style={{ width: searchWidth }}
            onSearch={handleSearch}
            className='rounded-lg'
          />
        </div>
      </Card>

      {/* Members Table */}
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}>
        <GalaxyTable
          data={members}
          columns={columns}
          loading={isLoading}
          customSort={true}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined,
          }}
          rowKey='id'
          scroll={isMobile ? { x: 800 } : undefined}
        />
      </Card>

      <MemberForm
        visible={showForm}
        onCancel={() => setShowForm(false)}
        onSuccess={() => {
          // Refresh data will happen automatically due to React Query invalidation
        }}
      />

      <MemberDetailsModal
        visible={showDetailsModal}
        member={selectedMember}
        onClose={handleCloseDetailsModal}
        onEdit={(member) => {
          // TODO: Implement edit functionality
          console.log('Edit member:', member);
        }}
      />
    </div>
  );
};

export default Members;
