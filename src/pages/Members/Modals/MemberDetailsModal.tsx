import React from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Avatar,
  Divider,
  Badge,
  Space,
  Button,
} from 'antd';
import {
  UserOutlined,
  IdcardOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  GlobalOutlined,
  HomeOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  FileTextOutlined,
  CloseOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useOrders } from '@/hooks/useOrders.ts';
import {
  formatDate,
  formatDateTime,
  formatCurrency,
} from '@/utils/tableUtils.ts';
import GalaxyTable from '@/components/GalaxyTable';
import type { Member } from '@/types';

const { Title, Text, Paragraph } = Typography;

interface MemberDetailsModalProps {
  visible: boolean;
  member: Member | null;
  onClose: () => void;
  onEdit?: (member: Member) => void;
}

const MemberDetailsModal: React.FC<MemberDetailsModalProps> = ({
  visible,
  member,
  onClose,
  onEdit,
}) => {
  // @ts-ignore
  const { t } = useTranslation();

  // Fetch member's orders
  const { data: ordersResponse, isLoading: ordersLoading } = useOrders({
    memberId: member?.id,
    page: 1,
    pageSize: 10,
  });

  const memberOrders = ordersResponse?.data?.data || [];

  if (!member) return null;

  const getMembershipTierColor = (tier?: string) => {
    switch (tier) {
      case 'bronze':
        return '#cd7f32';
      case 'silver':
        return '#c0c0c0';
      case 'gold':
        return '#ffd700';
      case 'platinum':
        return '#e5e4e2';
      default:
        return '#1890ff';
    }
  };

  const getMembershipTierIcon = (tier?: string) => {
    switch (tier) {
      case 'platinum':
        return <CrownOutlined style={{ color: '#e5e4e2' }} />;
      case 'gold':
        return <CrownOutlined style={{ color: '#ffd700' }} />;
      case 'silver':
        return <CrownOutlined style={{ color: '#c0c0c0' }} />;
      case 'bronze':
        return <CrownOutlined style={{ color: '#cd7f32' }} />;
      default:
        return <UserOutlined />;
    }
  };

  // const getStatusColor = (status: string) => {
  //   return status === 'active' ? 'green' : 'red';
  // };

  const orderColumns = [
    {
      title: t('orders.orderId'),
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: t('orders.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(amount)}
        </span>
      ),
    },
    {
      title: t('orders.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          pending: 'orange',
          completed: 'green',
          failed: 'red',
          cancelled: 'gray',
        };
        return (
          <Tag
            color={colors[status as keyof typeof colors]}
            className='font-medium'
          >
            {t(`orders.statuses.${status}`) ||
              status.charAt(0).toUpperCase() + status.slice(1)}
          </Tag>
        );
      },
    },
    {
      title: t('orders.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDateTime(date),
    },
  ];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      className='top-8'
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
    >
      <div className='p-6'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center gap-4'>
            <Avatar
              size={64}
              className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-xl'
            >
              {member.name.charAt(0).toUpperCase()}
            </Avatar>
            <div>
              <div className='flex items-center gap-3'>
                <Title level={3} className='text-gray-900 mb-0 font-bold'>
                  {member.name}
                </Title>
                <Badge
                  status={member.status === 'active' ? 'success' : 'error'}
                  text={
                    <span
                      className={`font-medium ${member.status === 'active' ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {member.status.charAt(0).toUpperCase() +
                        member.status.slice(1)}
                    </span>
                  }
                />
              </div>
              <div className='flex items-center gap-2 mt-1'>
                <Text className='text-gray-600'>Member ID: {member.id}</Text>
                {member.membershipTier && (
                  <Tag
                    icon={getMembershipTierIcon(member.membershipTier)}
                    color={getMembershipTierColor(member.membershipTier)}
                    className='font-medium'
                  >
                    {member.membershipTier.charAt(0).toUpperCase() +
                      member.membershipTier.slice(1)}
                  </Tag>
                )}
              </div>
            </div>
          </div>

          <Space>
            {onEdit && (
              <Button
                type='primary'
                icon={<EditOutlined />}
                onClick={() => onEdit(member)}
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('common.edit')}
              </Button>
            )}
          </Space>
        </div>

        <Divider className='my-6' />

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} className='mb-6'>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-blue-100 bg-blue-50/50'>
              <Statistic
                title={t('members.totalOrders')}
                value={member.totalOrders || 0}
                prefix={<ShoppingCartOutlined className='text-blue-600' />}
                valueStyle={{ color: '#1d4ed8', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-green-100 bg-green-50/50'>
              <Statistic
                title={t('members.totalSpent')}
                value={member.totalSpent || 0}
                prefix='$'
                precision={2}
                valueStyle={{ color: '#059669', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-purple-100 bg-purple-50/50'>
              <Statistic
                title={t('members.memberSince')}
                value={formatDate(member.registrationDate)}
                prefix={<CalendarOutlined className='text-purple-600' />}
                valueStyle={{ color: '#7c3aed', fontSize: '16px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-orange-100 bg-orange-50/50'>
              <Statistic
                title={t('members.lastVisit')}
                value={
                  member.lastVisit
                    ? formatDate(member.lastVisit)
                    : t('common.never')
                }
                prefix={<ClockCircleOutlined className='text-orange-600' />}
                valueStyle={{ color: '#ea580c', fontSize: '16px' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Member Details */}
        <Card
          className='mb-6'
          title={
            <span className='flex items-center gap-2'>
              <UserOutlined className='text-blue-600' />
              {t('members.personalInfo')}
            </span>
          }
        >
          <Descriptions column={2} size='middle'>
            <Descriptions.Item
              label={
                <span className='flex items-center gap-2'>
                  <IdcardOutlined className='text-gray-500' />
                  {t('members.passport')}
                </span>
              }
            >
              <span className='font-mono bg-gray-100 px-2 py-1 rounded'>
                {member.passport}
              </span>
            </Descriptions.Item>

            {member.email && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <MailOutlined className='text-gray-500' />
                    {t('members.email')}
                  </span>
                }
              >
                <a
                  href={`mailto:${member.email}`}
                  className='text-blue-600 hover:text-blue-800'
                >
                  {member.email}
                </a>
              </Descriptions.Item>
            )}

            {member.phone && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <PhoneOutlined className='text-gray-500' />
                    {t('members.phone')}
                  </span>
                }
              >
                <a
                  href={`tel:${member.phone}`}
                  className='text-blue-600 hover:text-blue-800'
                >
                  {member.phone}
                </a>
              </Descriptions.Item>
            )}

            {member.dateOfBirth && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <CalendarOutlined className='text-gray-500' />
                    {t('members.dateOfBirth')}
                  </span>
                }
              >
                {formatDate(member.dateOfBirth)}
              </Descriptions.Item>
            )}

            {member.nationality && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <GlobalOutlined className='text-gray-500' />
                    {t('members.nationality')}
                  </span>
                }
              >
                {member.nationality}
              </Descriptions.Item>
            )}

            <Descriptions.Item
              label={
                <span className='flex items-center gap-2'>
                  <CalendarOutlined className='text-gray-500' />
                  {t('members.registrationDate')}
                </span>
              }
            >
              {formatDateTime(member.registrationDate)}
            </Descriptions.Item>
          </Descriptions>

          {member.address && (
            <div className='mt-4'>
              <div className='flex items-center gap-2 mb-2'>
                <HomeOutlined className='text-gray-500' />
                <Text className='font-medium text-gray-700'>
                  {t('members.address')}
                </Text>
              </div>
              <Paragraph className='text-gray-600 ml-6 mb-0'>
                {member.address}
              </Paragraph>
            </div>
          )}

          {member.notes && (
            <div className='mt-4'>
              <div className='flex items-center gap-2 mb-2'>
                <FileTextOutlined className='text-gray-500' />
                <Text className='font-medium text-gray-700'>
                  {t('members.notes')}
                </Text>
              </div>
              <Paragraph className='text-gray-600 ml-6 mb-0'>
                {member.notes}
              </Paragraph>
            </div>
          )}
        </Card>

        {/* Recent Orders */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <ShoppingCartOutlined className='text-blue-600' />
              {t('members.recentOrders')}
            </span>
          }
          extra={
            <Text className='text-gray-500'>
              {t('common.showing')} {memberOrders.length} {t('common.orders')}
            </Text>
          }
        >
          {memberOrders.length > 0 ? (
            <GalaxyTable
              data={memberOrders}
              columns={orderColumns}
              loading={ordersLoading}
              pagination={undefined}
              rowKey='id'
              size='small'
            />
          ) : (
            <div className='text-center py-8'>
              <ShoppingCartOutlined className='text-4xl text-gray-300 mb-4' />
              <Text className='text-gray-500'>{t('members.noOrders')}</Text>
            </div>
          )}
        </Card>
      </div>
    </Modal>
  );
};

export default MemberDetailsModal;
