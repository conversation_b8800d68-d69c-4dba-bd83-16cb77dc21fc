import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined } from '@ant-design/icons';
import { ROUTES } from '@/constants/routes.ts';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleBackHome = () => {
    navigate(ROUTES.DASHBOARD);
  };

  return (
    <div className='flex items-center justify-center min-h-[60vh]'>
      <Result
        status='404'
        title='404'
        subTitle='Sorry, the page you visited does not exist.'
        extra={
          <Button
            type='primary'
            icon={<HomeOutlined />}
            onClick={handleBackHome}
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
          >
            Back to Dashboard
          </Button>
        }
      />
    </div>
  );
};

export default NotFound;
