import React, { useState } from 'react';
import { Tabs, Card, Row, Col, Statistic, Typography, Alert, Collapse } from 'antd';
import {
  CalculatorOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ReconciliationOutlined,
  DollarOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  StockOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useAccountingStats } from '@/hooks/useAccounting';
import FinancialStatements from '@/pages/Accounting/components/FinancialStatements';
import TransactionSummaries from '@/pages/Accounting/components/TransactionSummaries';
// import ReconciliationReports from '@/pages/Accounting/components/ReconciliationReports';
import { formatCurrency } from '@/utils/tableUtils';
import type { AccountingStats } from '@/types';

const { Title, Text } = Typography;

const Accounting: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('statements');
  const { data: statsResponse, isLoading, error } = useAccountingStats();

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-64'>
        <div className='text-center'>
          <CalculatorOutlined className='text-4xl text-blue-600 mb-4' />
          <div>{t('common.loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message={t('accounting.error')}
        description={t('accounting.errorDescription')}
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const stats: AccountingStats = statsResponse?.data || {
    totalRevenue: 1105000,
    totalExpenses: 420000,
    netIncome: 685000,
    totalAssets: 1800000,
    totalLiabilities: 1000000,
    equity: 800000,
    cashFlow: 285000,
    reconciliationStatus: {
      balanced: 15,
      variance: 3,
      pending: 2,
    },
  };

  const tabItems = [
    {
      key: 'statements',
      label: (
        <span className='flex items-center gap-2'>
          <FileTextOutlined />
          {t('accounting.financialStatements')}
        </span>
      ),
      children: <FinancialStatements />,
    },
    {
      key: 'summaries',
      label: (
        <span className='flex items-center gap-2'>
          <BarChartOutlined />
          {t('accounting.transactionSummaries')}
        </span>
      ),
      children: <TransactionSummaries />,
    },
    // {
    //   key: 'reconciliation',
    //   label: (
    //     <span className='flex items-center gap-2'>
    //       <ReconciliationOutlined />
    //       {t('accounting.reconciliationReports')}
    //     </span>
    //   ),
    //   children: <ReconciliationReports />,
    // },
  ];

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
        <div className='flex items-center gap-3'>
          <CalculatorOutlined className='text-blue-600 text-2xl' />
          <div>
            <Title level={2} className='text-gray-900 mb-0'>
              {t('accounting.title')}
            </Title>
            <Text className='text-gray-500'>{t('accounting.subtitle')}</Text>
          </div>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center border border-green-100 bg-green-50/50'>
            <Statistic
              title={t('accounting.totalRevenue')}
              value={stats.totalRevenue}
              prefix={<DollarOutlined className='text-green-600' />}
              precision={0}
              valueStyle={{
                color: '#059669',
                fontSize: '24px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
            <div className='mt-2 text-xs text-green-600'>
              +12.5% from last month
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center border border-red-100 bg-red-50/50'>
            <Statistic
              title={t('accounting.totalExpenses')}
              value={stats.totalExpenses}
              prefix={<DollarOutlined className='text-red-600' />}
              precision={0}
              valueStyle={{
                color: '#dc2626',
                fontSize: '24px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
            <div className='mt-2 text-xs text-red-600'>
              +3.2% from last month
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center border border-blue-100 bg-blue-50/50'>
            <Statistic
              title={t('accounting.netIncome')}
              value={stats.netIncome}
              prefix={<StockOutlined className='text-blue-600' />}
              precision={0}
              valueStyle={{
                color: '#1d4ed8',
                fontSize: '24px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
            <div className='mt-2 text-xs text-blue-600'>
              Profit margin:{' '}
              {Math.round((stats.netIncome / stats.totalRevenue) * 100)}%
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center border border-purple-100 bg-purple-50/50'>
            <Statistic
              title={t('accounting.cashFlow')}
              value={stats.cashFlow}
              prefix={<DollarOutlined className='text-purple-600' />}
              precision={0}
              valueStyle={{
                color: '#7c3aed',
                fontSize: '24px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
            <div className='mt-2 text-xs text-purple-600'>
              Operating cash flow
            </div>
          </Card>
        </Col>
      </Row>

      {/* Reconciliation Status */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <span className='flex items-center gap-2'>
                <ReconciliationOutlined className='text-blue-600' />
                {t('accounting.reconciliationStatus')}
              </span>
            }
            className='rounded-2xl border-0 shadow-card'
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div className='text-center p-4 bg-green-50 rounded-lg border border-green-200'>
                  <div className='text-2xl font-bold text-green-600'>
                    {stats.reconciliationStatus.balanced}
                  </div>
                  <div className='text-sm text-green-700 flex items-center justify-center gap-1'>
                    <CheckCircleOutlined />
                    {t('accounting.balanced')}
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div className='text-center p-4 bg-orange-50 rounded-lg border border-orange-200'>
                  <div className='text-2xl font-bold text-orange-600'>
                    {stats.reconciliationStatus.variance}
                  </div>
                  <div className='text-sm text-orange-700 flex items-center justify-center gap-1'>
                    <WarningOutlined />
                    {t('accounting.variance')}
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div className='text-center p-4 bg-blue-50 rounded-lg border border-blue-200'>
                  <div className='text-2xl font-bold text-blue-600'>
                    {stats.reconciliationStatus.pending}
                  </div>
                  <div className='text-sm text-blue-700 flex items-center justify-center gap-1'>
                    <CalculatorOutlined />
                    {t('accounting.pending')}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span className='flex items-center gap-2'>
                <BarChartOutlined className='text-blue-600' />
                {t('accounting.financialHealth')}
              </span>
            }
            className='rounded-2xl border-0 shadow-card'
          >
            <div className='space-y-4'>
              <div className='flex justify-between items-center'>
                <span className='text-gray-700'>{t('accounting.assets')}</span>
                <span className='font-medium text-blue-600'>
                  {formatCurrency(stats.totalAssets)}
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-gray-700'>
                  {t('accounting.liabilities')}
                </span>
                <span className='font-medium text-red-600'>
                  {formatCurrency(stats.totalLiabilities)}
                </span>
              </div>
              <div className='flex justify-between items-center border-t pt-2'>
                <span className='text-gray-900 font-medium'>
                  {t('accounting.equity')}
                </span>
                <span className='font-bold text-green-600'>
                  {formatCurrency(stats.equity)}
                </span>
              </div>
              <div className='text-xs text-gray-500 text-center'>
                Debt-to-Equity Ratio:{' '}
                {(stats.totalLiabilities / stats.equity).toFixed(2)}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content Tabs */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size='large'
          className='accounting-tabs'
        />
      </Card>
    </div>
  );
};

export default Accounting;
