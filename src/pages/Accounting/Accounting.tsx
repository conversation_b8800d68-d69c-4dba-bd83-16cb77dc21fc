import {
  CalculatorOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ReconciliationOutlined,
  DollarOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  StockOutlined,
  DownOutlined,
} from '@ant-design/icons';
import {
  Tabs,
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Alert,
  Collapse,
} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAccountingStats } from '@/hooks/useAccounting';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import FinancialStatements from '@/pages/Accounting/components/FinancialStatements';
import TransactionSummaries from '@/pages/Accounting/components/TransactionSummaries';
// import ReconciliationReports from '@/pages/Accounting/components/ReconciliationReports';
import type { AccountingStats } from '@/types';
import { formatCurrency } from '@/utils/tableUtils';

const { Title, Text } = Typography;

const Accounting: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile } = useResponsive();
  const [activeTab, setActiveTab] = useState<string>('statements');
  const { data: statsResponse, isLoading, error } = useAccountingStats();

  // Responsive values
  const statisticFontSize = useResponsiveValue({
    xs: '18px',
    sm: '20px',
    md: '24px',
    lg: '24px',
    xl: '24px',
    '2xl': '24px',
  });

  if (isLoading) {
    return (
      <div
        className={`flex justify-center items-center ${isMobile ? 'h-48' : 'h-64'}`}
      >
        <div className='text-center'>
          <CalculatorOutlined
            className={`${isMobile ? 'text-3xl' : 'text-4xl'} text-blue-600 mb-4`}
          />
          <div className={isMobile ? 'text-sm' : ''}>{t('common.loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message={t('accounting.error')}
        description={!isMobile ? t('accounting.errorDescription') : undefined}
        type='error'
        showIcon
        className={`${isMobile ? 'rounded-lg' : 'rounded-lg'}`}
      />
    );
  }

  const stats: AccountingStats = statsResponse?.data || {
    totalRevenue: 1105000,
    totalExpenses: 420000,
    netIncome: 685000,
    totalAssets: 1800000,
    totalLiabilities: 1000000,
    equity: 800000,
    cashFlow: 285000,
    reconciliationStatus: {
      balanced: 15,
      variance: 3,
      pending: 2,
    },
  };

  const tabItems = [
    {
      key: 'statements',
      label: (
        <span className='flex items-center gap-2'>
          <FileTextOutlined />
          {t('accounting.financialStatements')}
        </span>
      ),
      children: <FinancialStatements />,
    },
    {
      key: 'summaries',
      label: (
        <span className='flex items-center gap-2'>
          <BarChartOutlined />
          {t('accounting.transactionSummaries')}
        </span>
      ),
      children: <TransactionSummaries />,
    },
    // {
    //   key: 'reconciliation',
    //   label: (
    //     <span className='flex items-center gap-2'>
    //       <ReconciliationOutlined />
    //       {t('accounting.reconciliationReports')}
    //     </span>
    //   ),
    //   children: <ReconciliationReports />,
    // },
  ];

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header */}
      <div
        className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start sm:items-center justify-between`}
      >
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <CalculatorOutlined
            className={`text-blue-600 ${isMobile ? 'text-xl' : 'text-2xl'}`}
          />
          <div>
            <Title
              level={2}
              className={`text-gray-900 mb-0 ${isMobile ? 'text-lg' : ''}`}
            >
              {t('accounting.title')}
            </Title>
            <Text className={`text-gray-500 ${isMobile ? 'text-sm' : ''}`}>
              {t('accounting.subtitle')}
            </Text>
          </div>
        </div>
      </div>

      {/* Financial Overview Cards - Collapsible */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
        styles={{padding: isMobile ? '0px' : ''}}
      >
        <Collapse
          ghost
          expandIcon={({ isActive }) => (
            <DownOutlined
              rotate={isActive ? 180 : 0}
              className={`transition-transform duration-200 ${isMobile ? 'text-sm' : ''}`}
            />
          )}
          items={[
            {
              key: 'overview',
              label: (
                <div
                  className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}
                >
                  <div
                    className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-green-100 rounded-lg flex items-center justify-center`}
                  >
                    <DollarOutlined
                      className={`text-green-600 ${isMobile ? 'text-sm' : ''}`}
                    />
                  </div>
                  <div>
                    <Title
                      level={5}
                      className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-sm' : ''}`}
                    >
                      Financial Overview
                    </Title>
                    <Text
                      className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}
                    >
                      Key financial metrics and performance indicators
                    </Text>
                  </div>
                </div>
              ),
              children: (
                <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}>
                  <Row
                    gutter={[
                      { xs: 12, sm: 16, lg: 24 },
                      { xs: 12, sm: 16, lg: 24 },
                    ]}
                  >
                    <Col xs={24} sm={12} lg={6}>
                      <Card
                        className={`text-center border border-green-100 bg-green-50/50 ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span className={isMobile ? 'text-xs' : ''}>
                              {isMobile
                                ? 'Revenue'
                                : t('accounting.totalRevenue')}
                            </span>
                          }
                          value={stats.totalRevenue}
                          prefix={<DollarOutlined className='text-green-600' />}
                          precision={0}
                          valueStyle={{
                            color: '#059669',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                          formatter={(value) => formatCurrency(Number(value))}
                        />
                        <div
                          className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-green-600`}
                        >
                          {isMobile ? '+12.5%' : '+12.5% from last month'}
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card
                        className={`text-center border border-red-100 bg-red-50/50 ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span className={isMobile ? 'text-xs' : ''}>
                              {isMobile
                                ? 'Expenses'
                                : t('accounting.totalExpenses')}
                            </span>
                          }
                          value={stats.totalExpenses}
                          prefix={<DollarOutlined className='text-red-600' />}
                          precision={0}
                          valueStyle={{
                            color: '#dc2626',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                          formatter={(value) => formatCurrency(Number(value))}
                        />
                        <div
                          className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-red-600`}
                        >
                          {isMobile ? '****%' : '****% from last month'}
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card
                        className={`text-center border border-blue-100 bg-blue-50/50 ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span className={isMobile ? 'text-xs' : ''}>
                              {isMobile
                                ? 'Net Income'
                                : t('accounting.netIncome')}
                            </span>
                          }
                          value={stats.netIncome}
                          prefix={<StockOutlined className='text-blue-600' />}
                          precision={0}
                          valueStyle={{
                            color: '#1d4ed8',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                          formatter={(value) => formatCurrency(Number(value))}
                        />
                        <div
                          className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-blue-600`}
                        >
                          {isMobile ? 'Margin' : 'Profit margin'}:{' '}
                          {Math.round(
                            (stats.netIncome / stats.totalRevenue) * 100,
                          )}
                          %
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card
                        className={`text-center border border-purple-100 bg-purple-50/50 ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span className={isMobile ? 'text-xs' : ''}>
                              {isMobile
                                ? 'Cash Flow'
                                : t('accounting.cashFlow')}
                            </span>
                          }
                          value={stats.cashFlow}
                          prefix={
                            <DollarOutlined className='text-purple-600' />
                          }
                          precision={0}
                          valueStyle={{
                            color: '#7c3aed',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                          formatter={(value) => formatCurrency(Number(value))}
                        />
                        <div
                          className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-purple-600`}
                        >
                          {isMobile ? 'Operating' : 'Operating cash flow'}
                        </div>
                      </Card>
                    </Col>
                  </Row>
                  {/* Reconciliation Status */}
                  <Row
                    gutter={[
                      { xs: 12, sm: 16, lg: 24 },
                      { xs: 12, sm: 16, lg: 24 },
                    ]}
                  >
                    <Col xs={24} lg={16}>
                      <Card
                        title={
                          <span
                            className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}
                          >
                            <ReconciliationOutlined className='text-blue-600' />
                            <span className={isMobile ? 'text-sm' : ''}>
                              {t('accounting.reconciliationStatus')}
                            </span>
                          </span>
                        }
                        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
                      >
                        <Row
                          gutter={[
                            { xs: 8, sm: 12, lg: 16 },
                            { xs: 8, sm: 12, lg: 16 },
                          ]}
                        >
                          <Col xs={24} sm={8}>
                            <div
                              className={`text-center ${isMobile ? 'p-3' : 'p-4'} bg-green-50 rounded-lg border border-green-200`}
                            >
                              <div
                                className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-green-600`}
                              >
                                {stats.reconciliationStatus.balanced}
                              </div>
                              <div
                                className={`${isMobile ? 'text-xs' : 'text-sm'} text-green-700 flex items-center justify-center gap-1`}
                              >
                                <CheckCircleOutlined />
                                {t('accounting.balanced')}
                              </div>
                            </div>
                          </Col>
                          <Col xs={24} sm={8}>
                            <div
                              className={`text-center ${isMobile ? 'p-3' : 'p-4'} bg-orange-50 rounded-lg border border-orange-200`}
                            >
                              <div
                                className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-orange-600`}
                              >
                                {stats.reconciliationStatus.variance}
                              </div>
                              <div
                                className={`${isMobile ? 'text-xs' : 'text-sm'} text-orange-700 flex items-center justify-center gap-1`}
                              >
                                <WarningOutlined />
                                {t('accounting.variance')}
                              </div>
                            </div>
                          </Col>
                          <Col xs={24} sm={8}>
                            <div
                              className={`text-center ${isMobile ? 'p-3' : 'p-4'} bg-blue-50 rounded-lg border border-blue-200`}
                            >
                              <div
                                className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-blue-600`}
                              >
                                {stats.reconciliationStatus.pending}
                              </div>
                              <div
                                className={`${isMobile ? 'text-xs' : 'text-sm'} text-blue-700 flex items-center justify-center gap-1`}
                              >
                                <CalculatorOutlined />
                                {t('accounting.pending')}
                              </div>
                            </div>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                    <Col xs={24} lg={8}>
                      <Card
                        title={
                          <span
                            className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}
                          >
                            <BarChartOutlined className='text-blue-600' />
                            <span className={isMobile ? 'text-sm' : ''}>
                              {t('accounting.financialHealth')}
                            </span>
                          </span>
                        }
                        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
                      >
                        <div
                          className={`space-y-3 ${isMobile ? 'sm:space-y-3' : 'sm:space-y-4'}`}
                        >
                          <div className='flex justify-between items-center'>
                            <span
                              className={`text-gray-700 ${isMobile ? 'text-sm' : ''}`}
                            >
                              {t('accounting.assets')}
                            </span>
                            <span
                              className={`font-medium text-blue-600 ${isMobile ? 'text-sm' : ''}`}
                            >
                              {formatCurrency(stats.totalAssets)}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span
                              className={`text-gray-700 ${isMobile ? 'text-sm' : ''}`}
                            >
                              {t('accounting.liabilities')}
                            </span>
                            <span
                              className={`font-medium text-red-600 ${isMobile ? 'text-sm' : ''}`}
                            >
                              {formatCurrency(stats.totalLiabilities)}
                            </span>
                          </div>
                          <div className='flex justify-between items-center border-t pt-2'>
                            <span
                              className={`text-gray-900 font-medium ${isMobile ? 'text-sm' : ''}`}
                            >
                              {t('accounting.equity')}
                            </span>
                            <span
                              className={`font-bold text-green-600 ${isMobile ? 'text-sm' : ''}`}
                            >
                              {formatCurrency(stats.equity)}
                            </span>
                          </div>
                          <div
                            className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-500 text-center`}
                          >
                            {isMobile ? 'D/E Ratio' : 'Debt-to-Equity Ratio'}:{' '}
                            {(stats.totalLiabilities / stats.equity).toFixed(2)}
                          </div>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </div>
              ),
            },
          ]}
        />
      </Card>

      {/* Main Content Tabs */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size={isMobile ? 'small' : 'large'}
          className='accounting-tabs'
          tabPosition={isMobile ? 'top' : 'top'}
        />
      </Card>
    </div>
  );
};

export default Accounting;
