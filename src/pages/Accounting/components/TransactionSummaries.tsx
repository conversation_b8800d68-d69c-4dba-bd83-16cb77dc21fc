import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  DatePicker,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Tag,
  Collapse,
} from 'antd';
import {
  BarChartOutlined,
  DownloadOutlined,
  EyeOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  CreditCardOutlined,
  DollarOutlined,
  CaretRightOutlined,
  StockOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useTransactionSummaries } from '@/hooks/useAccounting.ts';
import GenerateSummaryModal from '@/pages/Accounting/components/modals/GenerateSummaryModal';
import { formatCurrency, formatDate } from '@/utils/tableUtils.ts';
import type { TransactionSummary, TransactionDetail } from '@/types';

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const TransactionSummaries: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [selectedSummary, setSelectedSummary] =
    useState<TransactionSummary | null>(null);
  const [dateRange, setDateRange] = useState<[string, string] | undefined>();
  const [showGenerateModal, setShowGenerateModal] = useState(false);

  // Responsive values
  const statisticFontSize = useResponsiveValue({
    xs: '16px',
    sm: '18px',
    md: '20px',
    lg: '20px',
    xl: '20px',
    '2xl': '20px'
  });

  const { data: summariesResponse, isLoading } = useTransactionSummaries({
    dateRange,
  });

  const summaries = summariesResponse?.data || [];

  const handleViewSummary = (summary: TransactionSummary) => {
    setSelectedSummary(summary);
  };

  const handleDownload = (summary: TransactionSummary) => {
    // In a real app, this would trigger a download
    console.log('Downloading summary:', summary.id);
  };

  const columns = [
    {
      title: t('accounting.period'),
      key: 'period',
      render: (_: any, record: TransactionSummary) => (
        <div>
          <div className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}>
            {isMobile
              ? formatDate(record.period.startDate).slice(0, 6)
              : formatDate(record.period.startDate)
            }
          </div>
          <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>
            {isMobile ? 'to' : 'to'} {isMobile
              ? formatDate(record.period.endDate).slice(0, 6)
              : formatDate(record.period.endDate)
            }
          </div>
        </div>
      ),
      width: isMobile ? 100 : undefined,
    },
    {
      title: t('accounting.totalTransactions'),
      dataIndex: ['data', 'transactionCount'],
      key: 'transactionCount',
      render: (_: any, record: TransactionSummary) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <ShoppingCartOutlined className={`text-blue-600 ${isMobile ? 'text-sm' : ''}`} />
          <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
            {record.summary.totalTransactions}
          </span>
        </div>
      ),
      sorter: (a: TransactionSummary, b: TransactionSummary) =>
        a.summary.totalTransactions - b.summary.totalTransactions,
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('accounting.totalAmount'),
      dataIndex: ['data', 'totalAmount'],
      key: 'totalAmount',
      render: (_: any, record: TransactionSummary) => (
        <span className={`font-bold text-green-600 ${isMobile ? 'text-sm' : ''}`}>
          {formatCurrency(record.summary.totalAmount)}
        </span>
      ),
      sorter: (a: TransactionSummary, b: TransactionSummary) =>
        a.summary.totalAmount - b.summary.totalAmount,
      width: isMobile ? 100 : undefined,
    },
    {
      title: t('accounting.averageTransaction'),
      dataIndex: ['data', 'averageAmount'],
      key: 'averageAmount',
      render: (_: any, record: TransactionSummary) => (
        <span className={`font-medium text-blue-600 ${isMobile ? 'text-sm' : ''}`}>
          {formatCurrency(record.summary.averageTransaction)}
        </span>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('accounting.generatedAt'),
      dataIndex: 'generatedAt',
      key: 'generatedAt',
      render: (date: string) => (
        <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
          {isMobile ? formatDate(date).slice(0, 10) : formatDate(date)}
        </div>
      ),
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: isMobile ? 60 : 120,
      render: (_: any, record: TransactionSummary) => (
        <Space size='small' direction={isMobile ? 'vertical' : 'horizontal'}>
          <Button
            type='text'
            icon={<EyeOutlined />}
            onClick={() => handleViewSummary(record)}
            className='text-blue-600 hover:text-blue-800'
            title={t('common.view')}
            size={isMobile ? 'small' : 'default'}
          >
            {!isMobile && t('common.view')}
          </Button>
          {!isMobile && (
            <Button
              type='text'
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
              className='text-green-600 hover:text-green-800'
              title={t('common.download')}
              size={isMobile ? 'small' : 'default'}
            />
          )}
        </Space>
      ),
    },
  ];

  const renderSummaryDetails = (summary: TransactionSummary) => {
    const {
      summary: data,
      transactions,
      transactionsByPaymentMethod,
      transactionsByEmployee,
      transactionsByRegister,
    } = summary;

    // Transaction detail table columns
    const transactionColumns = [
      {
        title: t('accounting.transactionId'),
        dataIndex: 'id',
        key: 'id',
        width: isMobile ? 80 : 120,
        render: (id: string) => (
          <span className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} text-blue-600`}>
            {isMobile ? id.slice(-6) : id}
          </span>
        ),
      },
      {
        title: t('accounting.timestamp'),
        dataIndex: 'timestamp',
        key: 'timestamp',
        width: isMobile ? 100 : 150,
        render: (timestamp: string) => (
          <div className={isMobile ? 'text-xs' : 'text-sm'}>
            <div>{formatDate(timestamp).split(' ')[0]}</div>
            {!isMobile && (
              <div className='text-gray-500'>
                {formatDate(timestamp).split(' ')[1]}
              </div>
            )}
          </div>
        ),
        responsive: isMobile ? ['md'] : undefined,
      },
      {
        title: t('accounting.amount'),
        dataIndex: 'amount',
        key: 'amount',
        width: isMobile ? 80 : 120,
        render: (amount: number) => (
          <span
            className={`font-bold ${isMobile ? 'text-xs' : 'text-sm'} ${
              amount >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatCurrency(Math.abs(amount))}
          </span>
        ),
        sorter: (a: TransactionDetail, b: TransactionDetail) =>
          a.amount - b.amount,
      },
      {
        title: t('accounting.paymentMethod'),
        dataIndex: 'paymentMethod',
        key: 'paymentMethod',
        width: isMobile ? 80 : 120,
        render: (method: string) => (
          <Tag color='blue' className={isMobile ? 'text-xs' : ''}>
            {isMobile ? method.slice(0, 4) : t(`orders.paymentMethods.${method}`)}
          </Tag>
        ),
        responsive: isMobile ? ['lg'] : undefined,
      },
      {
        title: t('accounting.employee'),
        dataIndex: 'employeeName',
        key: 'employeeName',
        width: isMobile ? 100 : 150,
        render: (name: string) => (
          <span className={isMobile ? 'text-xs' : 'text-sm'}>
            {isMobile && name ? name.split(' ')[0] : name}
          </span>
        ),
        responsive: isMobile ? ['lg'] : undefined,
      },
      {
        title: t('accounting.register'),
        dataIndex: 'registerName',
        key: 'registerName',
        width: isMobile ? 80 : 150,
        render: (name: string) => (
          <span className={isMobile ? 'text-xs' : 'text-sm'}>
            {isMobile && name ? name.slice(0, 8) : name}
          </span>
        ),
        responsive: isMobile ? ['xl'] : undefined,
      },
      {
        title: t('accounting.description'),
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
        render: (desc: string) => (
          <span className={isMobile ? 'text-xs' : 'text-sm'}>
            {desc}
          </span>
        ),
        responsive: isMobile ? ['xl'] : undefined,
      },
      {
        title: t('accounting.reference'),
        dataIndex: 'reference',
        key: 'reference',
        width: isMobile ? 60 : 120,
        render: (ref: string) => (
          <span className={`font-mono ${isMobile ? 'text-xs' : 'text-xs'} text-gray-600`}>
            {isMobile && ref ? ref.slice(-4) : ref}
          </span>
        ),
        responsive: isMobile ? ['xl'] : undefined,
      },
    ];

    const transactionTypeItems = [
      {
        key: 'byType',
        label: t('accounting.byTransactionType'),
        children: (
          <div className='space-y-4'>
            {Object.entries(data.byType).map(([type, info]) => {
              const typeTransactions =
                transactions[type as keyof typeof transactions] || [];

              return (
                <div key={type} className='space-y-3'>
                  {/* Summary Card */}
                  <div className='flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200'>
                    <div className='flex items-center gap-3'>
                      <ShoppingCartOutlined className='text-blue-600 text-xl' />
                      <div>
                        <div className='font-bold text-lg text-blue-900'>
                          {t(`transactions.types.${type}`)}
                        </div>
                        <div className='text-sm text-blue-700'>
                          {info.count} transactions •{' '}
                          {(
                            (info.count / data.totalTransactions) *
                            100
                          ).toFixed(1)}
                          % of total
                        </div>
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='font-bold text-2xl text-blue-900'>
                        {formatCurrency(info.amount)}
                      </div>
                      <div className='text-sm text-blue-700'>
                        Avg: {formatCurrency(info.amount / info.count)}
                      </div>
                    </div>
                  </div>

                  {/* Collapsible Transaction Details */}
                  <Collapse
                    items={[
                      {
                        key: `${type}-details`,
                        label: (
                          <span className='flex items-center gap-2 font-medium'>
                            <ShoppingCartOutlined className='text-blue-600' />
                            {t('accounting.viewTransactionDetails')} (
                            {typeTransactions.length})
                          </span>
                        ),
                        children:
                          typeTransactions.length > 0 ? (
                            <Table
                              dataSource={typeTransactions}
                              columns={transactionColumns}
                              rowKey='id'
                              size='small'
                              pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total, range) =>
                                  `${range[0]}-${range[1]} of ${total} transactions`,
                              }}
                              scroll={{ x: 800 }}
                            />
                          ) : (
                            <div className='text-center py-8 text-gray-500'>
                              {t('accounting.noTransactionsFound')}
                            </div>
                          ),
                      },
                    ]}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined
                        rotate={isActive ? 90 : 0}
                        className='text-blue-600'
                      />
                    )}
                    className='border border-gray-200 rounded-lg'
                    ghost
                  />
                </div>
              );
            })}
          </div>
        ),
      },
      {
        key: 'byPayment',
        label: t('accounting.byPaymentMethod'),
        children: (
          <div className='space-y-4'>
            {Object.entries(data.byPaymentMethod).map(([method, info]) => {
              const methodTransactions =
                transactionsByPaymentMethod[method] || [];

              return (
                <div key={method} className='space-y-3'>
                  {/* Summary Card */}
                  <div className='flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200'>
                    <div className='flex items-center gap-3'>
                      <CreditCardOutlined className='text-purple-600 text-xl' />
                      <div>
                        <div className='font-bold text-lg text-purple-900'>
                          {t(`orders.paymentMethods.${method}`)}
                        </div>
                        <div className='text-sm text-purple-700'>
                          {info.count} transactions •{' '}
                          {((info.amount / data.totalAmount) * 100).toFixed(1)}%
                          of total
                        </div>
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='font-bold text-2xl text-purple-900'>
                        {formatCurrency(info.amount)}
                      </div>
                      <div className='text-sm text-purple-700'>
                        Avg: {formatCurrency(info.amount / info.count)}
                      </div>
                    </div>
                  </div>

                  {/* Collapsible Transaction Details */}
                  <Collapse
                    items={[
                      {
                        key: `${method}-details`,
                        label: (
                          <span className='flex items-center gap-2 font-medium'>
                            <CreditCardOutlined className='text-purple-600' />
                            {t('accounting.viewTransactionDetails')} (
                            {methodTransactions.length})
                          </span>
                        ),
                        children:
                          methodTransactions.length > 0 ? (
                            <Table
                              dataSource={methodTransactions}
                              columns={transactionColumns}
                              rowKey='id'
                              size='small'
                              pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total, range) =>
                                  `${range[0]}-${range[1]} of ${total} transactions`,
                              }}
                              scroll={{ x: 800 }}
                            />
                          ) : (
                            <div className='text-center py-8 text-gray-500'>
                              {t('accounting.noTransactionsFound')}
                            </div>
                          ),
                      },
                    ]}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined
                        rotate={isActive ? 90 : 0}
                        className='text-purple-600'
                      />
                    )}
                    className='border border-gray-200 rounded-lg'
                    ghost
                  />
                </div>
              );
            })}
          </div>
        ),
      },
      {
        key: 'byEmployee',
        label: t('accounting.byEmployee'),
        children: (
          <div className='space-y-4'>
            {Object.entries(data.byEmployee).map(([empId, info]) => {
              const employeeTransactions = transactionsByEmployee[empId] || [];

              return (
                <div key={empId} className='space-y-3'>
                  {/* Summary Card */}
                  <div className='flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200'>
                    <div className='flex items-center gap-3'>
                      <UserOutlined className='text-orange-600 text-xl' />
                      <div>
                        <div className='font-bold text-lg text-orange-900'>
                          {info.employeeName}
                        </div>
                        <div className='text-sm text-orange-700'>
                          {info.count} transactions •{' '}
                          {((info.amount / data.totalAmount) * 100).toFixed(1)}%
                          of total
                        </div>
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='font-bold text-2xl text-orange-900'>
                        {formatCurrency(info.amount)}
                      </div>
                      <div className='text-sm text-orange-700'>
                        Avg: {formatCurrency(info.amount / info.count)}
                      </div>
                    </div>
                  </div>

                  {/* Collapsible Transaction Details */}
                  <Collapse
                    items={[
                      {
                        key: `${empId}-details`,
                        label: (
                          <span className='flex items-center gap-2 font-medium'>
                            <UserOutlined className='text-orange-600' />
                            {t('accounting.viewTransactionDetails')} (
                            {employeeTransactions.length})
                          </span>
                        ),
                        children:
                          employeeTransactions.length > 0 ? (
                            <Table
                              dataSource={employeeTransactions}
                              columns={transactionColumns}
                              rowKey='id'
                              size='small'
                              pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total, range) =>
                                  `${range[0]}-${range[1]} of ${total} transactions`,
                              }}
                              scroll={{ x: 800 }}
                            />
                          ) : (
                            <div className='text-center py-8 text-gray-500'>
                              {t('accounting.noTransactionsFound')}
                            </div>
                          ),
                      },
                    ]}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined
                        rotate={isActive ? 90 : 0}
                        className='text-orange-600'
                      />
                    )}
                    className='border border-gray-200 rounded-lg'
                    ghost
                  />
                </div>
              );
            })}
          </div>
        ),
      },
      {
        key: 'byRegister',
        label: t('accounting.byRegister'),
        children: (
          <div className='space-y-4'>
            {Object.entries(data.byRegister).map(([regId, info]) => {
              const registerTransactions = transactionsByRegister[regId] || [];

              return (
                <div key={regId} className='space-y-3'>
                  {/* Summary Card */}
                  <div className='flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200'>
                    <div className='flex items-center gap-3'>
                      <DollarOutlined className='text-green-600 text-xl' />
                      <div>
                        <div className='font-bold text-lg text-green-900'>
                          {info.registerName}
                        </div>
                        <div className='text-sm text-green-700'>
                          {info.count} transactions •{' '}
                          {((info.amount / data.totalAmount) * 100).toFixed(1)}%
                          of total
                        </div>
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='font-bold text-2xl text-green-900'>
                        {formatCurrency(info.amount)}
                      </div>
                      <div className='text-sm text-green-700'>
                        Avg: {formatCurrency(info.amount / info.count)}
                      </div>
                    </div>
                  </div>

                  {/* Collapsible Transaction Details */}
                  <Collapse
                    items={[
                      {
                        key: `${regId}-details`,
                        label: (
                          <span className='flex items-center gap-2 font-medium'>
                            <DollarOutlined className='text-green-600' />
                            {t('accounting.viewTransactionDetails')} (
                            {registerTransactions.length})
                          </span>
                        ),
                        children:
                          registerTransactions.length > 0 ? (
                            <Table
                              dataSource={registerTransactions}
                              columns={transactionColumns}
                              rowKey='id'
                              size='small'
                              pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total, range) =>
                                  `${range[0]}-${range[1]} of ${total} transactions`,
                              }}
                              scroll={{ x: 800 }}
                            />
                          ) : (
                            <div className='text-center py-8 text-gray-500'>
                              {t('accounting.noTransactionsFound')}
                            </div>
                          ),
                      },
                    ]}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined
                        rotate={isActive ? 90 : 0}
                        className='text-green-600'
                      />
                    )}
                    className='border border-gray-200 rounded-lg'
                    ghost
                  />
                </div>
              );
            })}
          </div>
        ),
      },
    ];

    return (
      <div className='space-y-6'>
        {/* Summary Header */}
        <div className='text-center border-b pb-4'>
          <Title level={3}>{t('accounting.transactionSummary')}</Title>
          <Text className='text-gray-600'>
            {formatDate(summary.period.startDate)} -{' '}
            {formatDate(summary.period.endDate)}
          </Text>
        </div>

        {/* Key Metrics */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-blue-200 bg-blue-50/50'>
              <Statistic
                title={t('accounting.totalTransactions')}
                value={data.totalTransactions}
                prefix={<ShoppingCartOutlined className='text-blue-600' />}
                valueStyle={{
                  color: '#1d4ed8',
                  fontSize: '20px',
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-green-200 bg-green-50/50'>
              <Statistic
                title={t('accounting.totalAmount')}
                value={data.totalAmount}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<DollarOutlined className='text-green-600' />}
                valueStyle={{
                  color: '#059669',
                  fontSize: '20px',
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-purple-200 bg-purple-50/50'>
              <Statistic
                title={t('accounting.averageTransaction')}
                value={data.averageTransaction}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<StockOutlined className='text-purple-600' />}
                valueStyle={{
                  color: '#7c3aed',
                  fontSize: '20px',
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-orange-200 bg-orange-50/50'>
              <Statistic
                title={t('accounting.transactionRate')}
                value={data.totalTransactions / 24} // Assuming 24-hour period
                precision={1}
                suffix='/hr'
                prefix={<BarChartOutlined className='text-orange-600' />}
                valueStyle={{
                  color: '#ea580c',
                  fontSize: '20px',
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col>
        </Row>

        {/* Detailed Breakdown */}
        <Card
          title={t('accounting.detailedBreakdown')}
          className='border-0 shadow-sm'
        >
          <Tabs items={transactionTypeItems} />
        </Card>
      </div>
    );
  };

  return (
    <div className='space-y-6'>
      {/* Filters */}
      <Card className='rounded-lg border-0 shadow-sm'>
        <div className='flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between'>
          <div className='flex flex-col sm:flex-row gap-4'>
            <RangePicker
              onChange={(dates) => {
                setDateRange(dates as unknown as [string, string]);
              }}
              className='w-64'
              placeholder={[t('common.startDate'), t('common.endDate')]}
            />
          </div>
          <Button
            type='primary'
            icon={<BarChartOutlined />}
            className='bg-blue-600 hover:bg-blue-700'
            onClick={() => setShowGenerateModal(true)}
          >
            {t('accounting.generateSummary')}
          </Button>
        </div>
      </Card>

      {/* Summary Details View */}
      {selectedSummary && (
        <Card
          title={
            <div className='flex items-center justify-between'>
              <span className='flex items-center gap-2'>
                <BarChartOutlined className='text-blue-600' />
                {t('accounting.summaryDetails')}
              </span>
              <Button
                onClick={() => setSelectedSummary(null)}
                className='text-gray-500'
              >
                {t('common.close')}
              </Button>
            </div>
          }
          className='rounded-lg border-0 shadow-sm'
        >
          {renderSummaryDetails(selectedSummary)}
        </Card>
      )}

      {/* Summaries Table */}
      <Card
        title={
          <span className='flex items-center gap-2'>
            <BarChartOutlined className='text-blue-600' />
            {t('accounting.transactionSummaries')}
          </span>
        }
        className='rounded-lg border-0 shadow-sm'
      >
        <Table
          dataSource={summaries}
          columns={columns}
          rowKey='id'
          loading={isLoading}
          scroll={{ x: 1000 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} summaries`,
          }}
        />
      </Card>

      {/* Generate Summary Modal */}
      <GenerateSummaryModal
        open={showGenerateModal}
        onCancel={() => setShowGenerateModal(false)}
      />
    </div>
  );
};

export default TransactionSummaries;
