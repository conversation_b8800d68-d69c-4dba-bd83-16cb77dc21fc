import React from 'react';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Button,
  Row,
  Col,
  Typography,
  Card,
  Checkbox,
} from 'antd';
import {
  BarChartOutlined,
  CalendarOutlined,
  SettingOutlined,
  UserOutlined,
  CreditCardOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useGenerateTransactionSummary } from '@/hooks/useAccounting.ts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

interface GenerateSummaryModalProps {
  open: boolean;
  onCancel: () => void;
}

const GenerateSummaryModal: React.FC<GenerateSummaryModalProps> = ({
  open,
  onCancel,
}) => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [form] = Form.useForm();
  const generateSummary = useGenerateTransactionSummary();

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '95%',
    md: '90%',
    lg: 700,
    xl: 700,
    '2xl': 700
  });

  const handleSubmit = async (values: any) => {
    //@ts-ignore
    const { dateRange, includeBreakdowns, filters } = values;
    console.log(includeBreakdowns, filters);
    const period = {
      startDate: dateRange[0].toISOString() as string,
      endDate: dateRange[1].toISOString() as string,
    };

    try {
      await generateSummary.mutateAsync({
        ...period,
      });
      form.resetFields();
      onCancel();
    } catch (error) {
      console.log(error);
      // Error is handled by the hook
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <BarChartOutlined className={`text-blue-600 ${isMobile ? 'text-lg' : 'text-xl'}`} />
          <div>
            <Title level={4} className={`mb-0 ${isMobile ? 'text-base' : ''}`}>
              {isMobile ? 'Generate Summary' : t('accounting.generateTransactionSummary')}
            </Title>
            <Text className={`text-gray-500 ${isMobile ? 'text-xs' : ''}`}>
              {isMobile ? 'Configure parameters' : t('accounting.configureSummaryParameters')}
            </Text>
          </div>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={modalWidth}
      centered
      style={{
        maxWidth: '95vw'
      }}
      footer={[
        <Button
          key='cancel'
          onClick={handleCancel}
          className={isMobile ? 'w-full mb-2' : ''}
          size={isMobile ? 'middle' : 'default'}
        >
          {t('common.cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          onClick={() => form.submit()}
          loading={generateSummary.isPending}
          className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
          size={isMobile ? 'middle' : 'default'}
        >
          {isMobile ? 'Generate' : t('accounting.generateSummary')}
        </Button>,
      ]}
      styles={{
        body: {
          padding: isMobile ? '12px' : '20px',
          maxHeight: isMobile ? '70vh' : '75vh',
          overflowY: 'auto'
        },
        footer: { padding: isMobile ? '12px' : '16px 20px' }
      }}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        initialValues={{
          includeBreakdowns: [
            'byType',
            'byPaymentMethod',
            'byEmployee',
            'byRegister',
          ],
        }}
      >
        <Card className={`${isMobile ? 'mb-3' : 'mb-4'} border border-blue-200 bg-blue-50/30`}>
          <Form.Item
            name='dateRange'
            label={
              <span className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
                <CalendarOutlined className='text-blue-600' />
                <span className={isMobile ? 'text-sm' : ''}>{t('accounting.summaryPeriod')}</span>
              </span>
            }
            rules={[
              {
                required: true,
                message: t('accounting.dateRangeRequired'),
              },
            ]}
            className={isMobile ? 'mb-2' : 'mb-4'}
          >
            <RangePicker
              size={isMobile ? 'middle' : 'large'}
              className='w-full'
              placeholder={[
                isMobile ? 'Start' : t('common.startDate'),
                isMobile ? 'End' : t('common.endDate')
              ]}
              showTime={!isMobile}
            />
          </Form.Item>
        </Card>

        <Card className={`${isMobile ? 'mb-3' : 'mb-4'} border border-green-200 bg-green-50/30`}>
          <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'mb-2' : 'mb-3'}`}>
            <SettingOutlined className='text-green-600' />
            <Title level={5} className={`mb-0 ${isMobile ? 'text-sm' : ''}`}>
              {isMobile ? 'Breakdowns' : t('accounting.includeBreakdowns')}
            </Title>
          </div>

          <Form.Item name='includeBreakdowns' className={isMobile ? 'mb-2' : 'mb-4'}>
            <Checkbox.Group className='w-full'>
              <Row gutter={[isMobile ? 6 : 12, isMobile ? 6 : 12]}>
                <Col xs={24} sm={12}>
                  <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'p-1.5' : 'p-2'} bg-white rounded border`}>
                    <Checkbox value='byType' />
                    <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
                      <BarChartOutlined className={`text-blue-600 ${isMobile ? 'text-sm' : ''}`} />
                      <div>
                        <div className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {isMobile ? 'By Type' : t('accounting.byTransactionType')}
                        </div>
                        {!isMobile && (
                          <div className='text-xs text-gray-500'>
                            {t('accounting.byTransactionTypeDescription')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'p-1.5' : 'p-2'} bg-white rounded border`}>
                    <Checkbox value='byPaymentMethod' />
                    <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
                      <CreditCardOutlined className={`text-purple-600 ${isMobile ? 'text-sm' : ''}`} />
                      <div>
                        <div className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {isMobile ? 'By Payment' : t('accounting.byPaymentMethod')}
                        </div>
                        {!isMobile && (
                          <div className='text-xs text-gray-500'>
                            {t('accounting.byPaymentMethodDescription')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'p-1.5' : 'p-2'} bg-white rounded border`}>
                    <Checkbox value='byEmployee' />
                    <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
                      <UserOutlined className={`text-orange-600 ${isMobile ? 'text-sm' : ''}`} />
                      <div>
                        <div className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {isMobile ? 'By Employee' : t('accounting.byEmployee')}
                        </div>
                        {!isMobile && (
                          <div className='text-xs text-gray-500'>
                            {t('accounting.byEmployeeDescription')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'p-1.5' : 'p-2'} bg-white rounded border`}>
                    <Checkbox value='byRegister' />
                    <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
                      <DollarOutlined className={`text-green-600 ${isMobile ? 'text-sm' : ''}`} />
                      <div>
                        <div className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {isMobile ? 'By Register' : t('accounting.byRegister')}
                        </div>
                        {!isMobile && (
                          <div className='text-xs text-gray-500'>
                            {t('accounting.byRegisterDescription')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Card>

        <Card className='border border-purple-200 bg-purple-50/30'>
          <div className='flex items-center gap-2 mb-4'>
            <SettingOutlined className='text-purple-600' />
            <Title level={5} className='mb-0'>
              {t('accounting.filterOptions')}
            </Title>
          </div>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name={['filters', 'minAmount']}
                label={t('accounting.minimumAmount')}
              >
                <Select
                  placeholder={t('accounting.selectMinimumAmount')}
                  allowClear
                >
                  <Option value={0}>{t('accounting.noMinimum')}</Option>
                  <Option value={10}>$10+</Option>
                  <Option value={50}>$50+</Option>
                  <Option value={100}>$100+</Option>
                  <Option value={500}>$500+</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name={['filters', 'transactionTypes']}
                label={t('accounting.includeTransactionTypes')}
              >
                <Select
                  mode='multiple'
                  placeholder={t('accounting.selectTransactionTypes')}
                  allowClear
                >
                  <Option value='sale'>{t('transactions.types.sale')}</Option>
                  <Option value='refund'>
                    {t('transactions.types.refund')}
                  </Option>
                  <Option value='void'>{t('transactions.types.void')}</Option>
                  <Option value='cash_in'>
                    {t('transactions.types.cash_in')}
                  </Option>
                  <Option value='cash_out'>
                    {t('transactions.types.cash_out')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default GenerateSummaryModal;
