import {
  FileTextOutlined,
  CalendarOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Button,
  Row,
  Col,
  Typography,
  Card,
  Checkbox,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useGenerateFinancialStatement } from '@/hooks/useAccounting.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

interface GenerateStatementModalProps {
  open: boolean;
  onCancel: () => void;
}

const GenerateStatementModal: React.FC<GenerateStatementModalProps> = ({
  open,
  onCancel,
}) => {
  const { t } = useTranslation();
  const { isMobile } = useResponsive();
  const [form] = Form.useForm();
  const generateStatement = useGenerateFinancialStatement();

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '700px',
    lg: '700px',
    xl: '700px',
    '2xl': '700px',
  });

  const handleSubmit = async (values: any) => {
    const {
      statementType,
      periodType,
      dateRange,
      includeComparisons,
      includeNotes,
      detailedBreakdown,
    } = values;
    const period = {
      startDate: dateRange[0].toISOString(),
      endDate: dateRange[1].toISOString(),
      type: periodType,
    };

    try {
      await generateStatement.mutateAsync({
        type: statementType,
        period,
        options: {
          includeComparisons,
          includeNotes,
          detailedBreakdown,
        },
      });
      form.resetFields();
      onCancel();
    } catch (error) {
      console.log(error);
      // Error is handled by the hook
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleItemClick = (fieldName: string) => {
    form.setFieldsValue({
      [fieldName]: !form.getFieldValue(fieldName),
    });
  };
  return (
    <Modal
      title={
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <FileTextOutlined
            className={`text-blue-600 ${isMobile ? 'text-lg' : 'text-xl'}`}
          />
          <div>
            <Title level={4} className={`mb-0 ${isMobile ? 'text-base' : ''}`}>
              {isMobile
                ? 'Generate Statement'
                : t('accounting.generateFinancialStatement')}
            </Title>
            <Text className={`text-gray-500 ${isMobile ? 'text-xs' : ''}`}>
              {isMobile
                ? 'Configure parameters'
                : t('accounting.configureStatementParameters')}
            </Text>
          </div>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      footer={[
        <Button
          key='cancel'
          onClick={handleCancel}
          className={isMobile ? 'w-full mb-2' : ''}
          size={isMobile ? 'middle' : 'large'}
        >
          {t('common.cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          onClick={() => form.submit()}
          loading={generateStatement.isPending}
          className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
          size={isMobile ? 'middle' : 'large'}
        >
          {isMobile ? 'Generate' : t('accounting.generateStatement')}
        </Button>,
      ]}
      styles={{
        body: { padding: isMobile ? '16px' : '24px' },
        footer: { padding: isMobile ? '16px' : '16px 24px' },
      }}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        initialValues={{
          periodType: 'monthly',
          includeComparisons: true,
          includeNotes: true,
          detailedBreakdown: true,
        }}
      >
        <Card
          className={`${isMobile ? 'mb-4' : 'mb-6'} border border-blue-200 bg-blue-50/30`}
          styles={{body: {padding: isMobile ? '0px' : ''}}}
        >
          <Row gutter={[isMobile ? 8 : 16, isMobile ? 8 : 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='statementType'
                label={
                  <span
                    className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
                  >
                    <FileTextOutlined className='text-blue-600' />
                    <span className={isMobile ? 'text-sm' : ''}>
                      {t('accounting.statementType')}
                    </span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('accounting.statementTypeRequired'),
                  },
                ]}
              >
                <Select
                  placeholder={t('accounting.selectStatementType')}
                  size={isMobile ? 'middle' : 'large'}
                >
                  <Option value='income_statement'>
                    {isMobile ? 'Income' : t('accounting.incomeStatement')}
                  </Option>
                  <Option value='balance_sheet'>
                    {isMobile ? 'Balance' : t('accounting.balanceSheet')}
                  </Option>
                  <Option value='cash_flow'>
                    {isMobile ? 'Cash Flow' : t('accounting.cashFlow')}
                  </Option>
                  <Option value='trial_balance'>
                    {isMobile ? 'Trial' : t('accounting.trialBalance')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='periodType'
                label={
                  <span
                    className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
                  >
                    <CalendarOutlined className='text-blue-600' />
                    <span className={isMobile ? 'text-sm' : ''}>
                      {t('accounting.periodType')}
                    </span>
                  </span>
                }
              >
                <Select size={isMobile ? 'middle' : 'large'}>
                  <Option value='daily'>{t('accounting.periods.daily')}</Option>
                  <Option value='weekly'>
                    {t('accounting.periods.weekly')}
                  </Option>
                  <Option value='monthly'>
                    {t('accounting.periods.monthly')}
                  </Option>
                  <Option value='quarterly'>
                    {t('accounting.periods.quarterly')}
                  </Option>
                  <Option value='yearly'>
                    {t('accounting.periods.yearly')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <Card
          className={`${isMobile ? 'mb-4' : 'mb-6'} border border-green-200 bg-green-50/30`}
        >
          <Form.Item
            name='dateRange'
            label={
              <span
                className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
              >
                <CalendarOutlined className='text-green-600' />
                <span className={isMobile ? 'text-sm' : ''}>
                  {t('accounting.reportingPeriod')}
                </span>
              </span>
            }
            rules={[
              {
                required: true,
                message: t('accounting.dateRangeRequired'),
              },
            ]}
          >
            <RangePicker
              size={isMobile ? 'middle' : 'large'}
              className='w-full'
              placeholder={[
                isMobile ? 'Start' : t('common.startDate'),
                isMobile ? 'End' : t('common.endDate'),
              ]}
            />
          </Form.Item>
        </Card>

        <Card className='border border-purple-200 bg-purple-50/30'>
          <div
            className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} ${isMobile ? 'mb-3' : 'mb-4'}`}
          >
            <SettingOutlined className='text-purple-600' />
            <Title level={5} className={`mb-0 ${isMobile ? 'text-sm' : ''}`}>
              {isMobile ? 'Options' : t('accounting.additionalOptions')}
            </Title>
          </div>

          <div
            className={`flex items-center cursor-pointer ${isMobile ? 'gap-2' : 'gap-3'} ${isMobile ? 'p-2' : 'p-3'} ${isMobile ? 'my-3' : 'my-6'} bg-white rounded-lg border`}
            onClick={() => handleItemClick('includeComparisons')}
          >
            <Form.Item
              name='includeComparisons'
              valuePropName='checked'
              noStyle
            >
              <Checkbox
                className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} text-blue-600 rounded`}
              />
            </Form.Item>
            <div>
              <div className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                {isMobile
                  ? 'Include Comparisons'
                  : t('accounting.includeComparisons')}
              </div>
              {!isMobile && (
                <div className='text-sm text-gray-500'>
                  {t('accounting.includeComparisonsDescription')}
                </div>
              )}
            </div>
          </div>

          {/* includeNotes */}
          <div
            className={`flex items-center cursor-pointer ${isMobile ? 'gap-2' : 'gap-3'} ${isMobile ? 'p-2' : 'p-3'} ${isMobile ? 'my-3' : 'my-6'} bg-white rounded-lg border`}
            onClick={() => handleItemClick('includeNotes')}
          >
            <Form.Item name='includeNotes' valuePropName='checked' noStyle>
              <Checkbox
                className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} text-blue-600 rounded`}
              />
            </Form.Item>
            <div>
              <div className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                {isMobile ? 'Include Notes' : t('accounting.includeNotes')}
              </div>
              {!isMobile && (
                <div className='text-sm text-gray-500'>
                  {t('accounting.includeNotesDescription')}
                </div>
              )}
            </div>
          </div>

          {/* detailedBreakdown */}
          <div
            className={`flex items-center cursor-pointer ${isMobile ? 'gap-2' : 'gap-3'} ${isMobile ? 'p-2' : 'p-3'} ${isMobile ? 'my-3' : 'my-6'} bg-white rounded-lg border`}
            onClick={() => handleItemClick('detailedBreakdown')}
          >
            <Form.Item name='detailedBreakdown' valuePropName='checked' noStyle>
              <Checkbox
                className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} text-blue-600 rounded`}
              />
            </Form.Item>
            <div>
              <div className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                {isMobile
                  ? 'Detailed Breakdown'
                  : t('accounting.detailedBreakdown')}
              </div>
              {!isMobile && (
                <div className='text-sm text-gray-500'>
                  {t('accounting.detailedBreakdownDescription')}
                </div>
              )}
            </div>
          </div>
        </Card>
      </Form>
    </Modal>
  );
};

export default GenerateStatementModal;
