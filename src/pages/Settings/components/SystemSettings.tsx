import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Space,
  message,
  Progress,
  Statistic,
  Alert,
  Select,
  InputNumber
} from 'antd';
import {
  SaveOutlined,
  DatabaseOutlined,
  HddOutlined,
  WifiOutlined,
  SettingOutlined,
  ReloadOutlined,
  WarningOutlined, CloudDownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;
const { Option } = Select;

interface SystemData {
  serverName: string;
  maxConnections: number;
  connectionTimeout: number;
  enableLogging: boolean;
  logLevel: string;
  enableCaching: boolean;
  cacheSize: number;
  enableCompression: boolean;
  compressionLevel: number;
  enableSSL: boolean;
  sslPort: number;
}

const SystemSettings: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Mock system stats
  const systemStats = {
    cpuUsage: 45,
    memoryUsage: 68,
    diskUsage: 72,
    networkUsage: 23,
    uptime: '15 days, 8 hours',
    activeConnections: 127,
    totalRequests: 1547892,
    errorRate: 0.02,
  };

  const initialData: SystemData = {
    serverName: 'Galaxy-Admin-Server',
    maxConnections: 1000,
    connectionTimeout: 30,
    enableLogging: true,
    logLevel: 'info',
    enableCaching: true,
    cacheSize: 512,
    enableCompression: true,
    compressionLevel: 6,
    enableSSL: true,
    sslPort: 443,
  };
  //@ts-expect-error
  const handleSave = async (values: SystemData) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.system.saveSuccess'));
    } catch (error) {
      message.error(t('settings.system.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleRestart = () => {
    message.warning(t('settings.system.restartWarning'));
  };

  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <CloudDownloadOutlined className="text-blue-600" />
            {t('settings.system.systemStatus')}
          </span>
        }
        className="rounded-lg"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <div className="text-center">
              <Progress 
                type="circle" 
                percent={systemStats.cpuUsage} 
                size={80}
                strokeColor={systemStats.cpuUsage > 80 ? '#ff4d4f' : '#52c41a'}
              />
              <div className="mt-2">
                <Text className="font-medium">{t('settings.system.cpuUsage')}</Text>
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <div className="text-center">
              <Progress 
                type="circle" 
                percent={systemStats.memoryUsage} 
                size={80}
                strokeColor={systemStats.memoryUsage > 80 ? '#ff4d4f' : '#52c41a'}
              />
              <div className="mt-2">
                <Text className="font-medium">{t('settings.system.memoryUsage')}</Text>
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <div className="text-center">
              <Progress 
                type="circle" 
                percent={systemStats.diskUsage} 
                size={80}
                strokeColor={systemStats.diskUsage > 80 ? '#ff4d4f' : '#52c41a'}
              />
              <div className="mt-2">
                <Text className="font-medium">{t('settings.system.diskUsage')}</Text>
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <div className="text-center">
              <Progress 
                type="circle" 
                percent={systemStats.networkUsage} 
                size={80}
                strokeColor="#1890ff"
              />
              <div className="mt-2">
                <Text className="font-medium">{t('settings.system.networkUsage')}</Text>
              </div>
            </div>
          </Col>
        </Row>
        
        <Row gutter={[24, 16]} className="mt-6">
          <Col xs={24} sm={8}>
            <Statistic 
              title={t('settings.system.uptime')} 
              value={systemStats.uptime} 
              prefix={<CloudDownloadOutlined />}
            />
          </Col>
          <Col xs={24} sm={8}>
            <Statistic 
              title={t('settings.system.activeConnections')} 
              value={systemStats.activeConnections} 
              prefix={<WifiOutlined />}
            />
          </Col>
          <Col xs={24} sm={8}>
            <Statistic 
              title={t('settings.system.errorRate')} 
              value={systemStats.errorRate} 
              suffix="%" 
              precision={2}
              prefix={<WarningOutlined />}
              valueStyle={{ color: systemStats.errorRate > 1 ? '#ff4d4f' : '#52c41a' }}
            />
          </Col>
        </Row>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialData}
        onFinish={handleSave}
        className="space-y-6"
      >
        {/* Server Configuration */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <SettingOutlined className="text-blue-600" />
              {t('settings.system.serverConfig')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name="serverName"
                label={t('settings.system.serverName')}
                rules={[{ required: true, message: t('settings.system.serverNameRequired') }]}
              >
                <Input size="large" />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="maxConnections"
                label={t('settings.system.maxConnections')}
                rules={[{ required: true, message: t('settings.system.maxConnectionsRequired') }]}
              >
                <InputNumber size="large" min={1} max={10000} className="w-full" />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="connectionTimeout"
                label={t('settings.system.connectionTimeout')}
                rules={[{ required: true, message: t('settings.system.connectionTimeoutRequired') }]}
              >
                <InputNumber 
                  size="large" 
                  min={1} 
                  max={300} 
                  className="w-full"
                  addonAfter={t('settings.system.seconds')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="sslPort"
                label={t('settings.system.sslPort')}
                dependencies={['enableSSL']}
              >
                <InputNumber 
                  size="large" 
                  min={1} 
                  max={65535} 
                  className="w-full"
                  disabled={!form.getFieldValue('enableSSL')}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Logging Configuration */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <HddOutlined className="text-blue-600" />
              {t('settings.system.loggingConfig')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.system.enableLogging')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.system.enableLoggingDesc')}
                </div>
              </div>
              <Form.Item name="enableLogging" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="logLevel"
              label={t('settings.system.logLevel')}
              dependencies={['enableLogging']}
            >
              <Select 
                size="large"
                disabled={!form.getFieldValue('enableLogging')}
              >
                <Option value="debug">Debug</Option>
                <Option value="info">Info</Option>
                <Option value="warn">Warning</Option>
                <Option value="error">Error</Option>
              </Select>
            </Form.Item>
          </div>
        </Card>

        {/* Performance Configuration */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <DatabaseOutlined className="text-blue-600" />
              {t('settings.system.performanceConfig')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.system.enableCaching')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.system.enableCachingDesc')}
                </div>
              </div>
              <Form.Item name="enableCaching" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="cacheSize"
              label={t('settings.system.cacheSize')}
              dependencies={['enableCaching']}
            >
              <InputNumber 
                size="large" 
                min={64} 
                max={4096} 
                className="w-full"
                disabled={!form.getFieldValue('enableCaching')}
                addonAfter="MB"
              />
            </Form.Item>
            
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.system.enableCompression')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.system.enableCompressionDesc')}
                </div>
              </div>
              <Form.Item name="enableCompression" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="compressionLevel"
              label={t('settings.system.compressionLevel')}
              dependencies={['enableCompression']}
            >
              <Select 
                size="large"
                disabled={!form.getFieldValue('enableCompression')}
              >
                <Option value={1}>1 - {t('settings.system.fastest')}</Option>
                <Option value={3}>3 - {t('settings.system.fast')}</Option>
                <Option value={6}>6 - {t('settings.system.balanced')}</Option>
                <Option value={9}>9 - {t('settings.system.best')}</Option>
              </Select>
            </Form.Item>
          </div>
        </Card>

        {/* Security Configuration */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <WarningOutlined className="text-blue-600" />
              {t('settings.system.securityConfig')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.system.enableSSL')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.system.enableSSLDesc')}
                </div>
              </div>
              <Form.Item name="enableSSL" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Alert
              message={t('settings.system.securityWarning')}
              description={t('settings.system.securityWarningDesc')}
              type="warning"
              showIcon
            />
          </div>
        </Card>

        {/* Action Buttons */}
        <Card className="rounded-lg">
          <div className="flex justify-between">
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRestart}
              size="large"
              danger
            >
              {t('settings.system.restartServer')}
            </Button>
            
            <Space>
              <Button size="large">
                {t('common.cancel')}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('settings.system.saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default SystemSettings;
