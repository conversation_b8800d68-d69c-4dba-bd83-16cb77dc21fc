import React, { useState } from 'react';
import {
  Card,
  Form,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Space,
  message,
  Select,
  Slider,
  Progress,
  Statistic,
  Alert,
  InputNumber
} from 'antd';
import {
  SaveOutlined,
  <PERSON>boltOutlined,
  DashboardOutlined,
  RocketOutlined,
  Bar<PERSON><PERSON>Outlined,
  ClockCircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

interface PerformanceData {
  enableOptimization: boolean;
  cacheLevel: number;
  compressionEnabled: boolean;
  compressionLevel: number;
  lazyLoading: boolean;
  imageOptimization: boolean;
  minifyAssets: boolean;
  enableCDN: boolean;
  maxConcurrentRequests: number;
  requestTimeout: number;
  enablePrefetch: boolean;
  enableServiceWorker: boolean;
}

const PerformanceSettings: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Mock performance metrics
  const performanceMetrics = {
    pageLoadTime: 1.2,
    firstContentfulPaint: 0.8,
    largestContentfulPaint: 1.5,
    cumulativeLayoutShift: 0.05,
    firstInputDelay: 12,
    performanceScore: 92,
  };

  const initialData: PerformanceData = {
    enableOptimization: true,
    cacheLevel: 3,
    compressionEnabled: true,
    compressionLevel: 6,
    lazyLoading: true,
    imageOptimization: true,
    minifyAssets: true,
    enableCDN: false,
    maxConcurrentRequests: 10,
    requestTimeout: 30,
    enablePrefetch: true,
    enableServiceWorker: true,
  };

  const handleSave = async (values: PerformanceData) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.performance.saveSuccess'));
    } catch (error) {
      message.error(t('settings.performance.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleOptimize = async () => {
    message.loading(t('settings.performance.optimizing'), 2);
    setTimeout(() => {
      message.success(t('settings.performance.optimizeSuccess'));
    }, 2000);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  return (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <DashboardOutlined className="text-blue-600" />
            {t('settings.performance.performanceMetrics')}
          </span>
        }
        className="rounded-lg"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12} lg={4}>
            <div className="text-center">
              <Progress 
                type="circle" 
                percent={performanceMetrics.performanceScore} 
                size={80}
                strokeColor={getScoreColor(performanceMetrics.performanceScore)}
              />
              <div className="mt-2">
                <Text className="font-medium">{t('settings.performance.overallScore')}</Text>
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={5}>
            <Statistic 
              title={t('settings.performance.pageLoadTime')} 
              value={performanceMetrics.pageLoadTime} 
              suffix="s"
              precision={1}
              valueStyle={{ color: performanceMetrics.pageLoadTime < 2 ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
          <Col xs={24} sm={12} lg={5}>
            <Statistic 
              title={t('settings.performance.firstContentfulPaint')} 
              value={performanceMetrics.firstContentfulPaint} 
              suffix="s"
              precision={1}
              valueStyle={{ color: performanceMetrics.firstContentfulPaint < 1.5 ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
          <Col xs={24} sm={12} lg={5}>
            <Statistic 
              title={t('settings.performance.largestContentfulPaint')} 
              value={performanceMetrics.largestContentfulPaint} 
              suffix="s"
              precision={1}
              valueStyle={{ color: performanceMetrics.largestContentfulPaint < 2.5 ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
          <Col xs={24} sm={12} lg={5}>
            <Statistic 
              title={t('settings.performance.firstInputDelay')} 
              value={performanceMetrics.firstInputDelay} 
              suffix="ms"
              valueStyle={{ color: performanceMetrics.firstInputDelay < 100 ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
        </Row>
        
        <div className="mt-4 text-center">
          <Button
            type="primary"
            icon={<RocketOutlined />}
            onClick={handleOptimize}
            size="large"
            className="bg-green-600 hover:bg-green-700"
          >
            {t('settings.performance.optimizeNow')}
          </Button>
        </div>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialData}
        onFinish={handleSave}
        className="space-y-6"
      >
        {/* General Optimization */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <ThunderboltOutlined className="text-blue-600" />
              {t('settings.performance.generalOptimization')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.performance.enableOptimization')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.performance.enableOptimizationDesc')}
                </div>
              </div>
              <Form.Item name="enableOptimization" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="cacheLevel"
              label={t('settings.performance.cacheLevel')}
              dependencies={['enableOptimization']}
            >
              <div className="px-2">
                <Slider
                  min={1}
                  max={5}
                  marks={{
                    1: t('settings.performance.cacheLevels.minimal'),
                    2: t('settings.performance.cacheLevels.basic'),
                    3: t('settings.performance.cacheLevels.standard'),
                    4: t('settings.performance.cacheLevels.aggressive'),
                    5: t('settings.performance.cacheLevels.maximum'),
                  }}
                  disabled={!form.getFieldValue('enableOptimization')}
                />
              </div>
            </Form.Item>
          </div>
        </Card>

        {/* Compression Settings */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <DatabaseOutlined className="text-blue-600" />
              {t('settings.performance.compressionSettings')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.performance.enableCompression')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.performance.enableCompressionDesc')}
                </div>
              </div>
              <Form.Item name="compressionEnabled" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="compressionLevel"
              label={t('settings.performance.compressionLevel')}
              dependencies={['compressionEnabled']}
            >
              <Select 
                size="large"
                disabled={!form.getFieldValue('compressionEnabled')}
              >
                <Option value={1}>1 - {t('settings.performance.compressionLevels.fastest')}</Option>
                <Option value={3}>3 - {t('settings.performance.compressionLevels.fast')}</Option>
                <Option value={6}>6 - {t('settings.performance.compressionLevels.balanced')}</Option>
                <Option value={9}>9 - {t('settings.performance.compressionLevels.best')}</Option>
              </Select>
            </Form.Item>
          </div>
        </Card>

        {/* Asset Optimization */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <BarChartOutlined className="text-blue-600" />
              {t('settings.performance.assetOptimization')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.lazyLoading')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.lazyLoadingDesc')}
                  </div>
                </div>
                <Form.Item name="lazyLoading" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.imageOptimization')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.imageOptimizationDesc')}
                  </div>
                </div>
                <Form.Item name="imageOptimization" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.minifyAssets')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.minifyAssetsDesc')}
                  </div>
                </div>
                <Form.Item name="minifyAssets" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.enableCDN')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.enableCDNDesc')}
                  </div>
                </div>
                <Form.Item name="enableCDN" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Network Settings */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <ClockCircleOutlined className="text-blue-600" />
              {t('settings.performance.networkSettings')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name="maxConcurrentRequests"
                label={t('settings.performance.maxConcurrentRequests')}
                rules={[{ required: true, message: t('settings.performance.maxConcurrentRequestsRequired') }]}
              >
                <InputNumber 
                  size="large" 
                  min={1} 
                  max={50} 
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="requestTimeout"
                label={t('settings.performance.requestTimeout')}
                rules={[{ required: true, message: t('settings.performance.requestTimeoutRequired') }]}
              >
                <InputNumber 
                  size="large" 
                  min={5} 
                  max={300} 
                  className="w-full"
                  addonAfter={t('settings.performance.seconds')}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.enablePrefetch')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.enablePrefetchDesc')}
                  </div>
                </div>
                <Form.Item name="enablePrefetch" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium">{t('settings.performance.enableServiceWorker')}</Text>
                  <div className="text-sm text-gray-500">
                    {t('settings.performance.enableServiceWorkerDesc')}
                  </div>
                </div>
                <Form.Item name="enableServiceWorker" valuePropName="checked" className="mb-0">
                  <Switch />
                </Form.Item>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Performance Tips */}
        <Alert
          message={t('settings.performance.performanceTips')}
          description={t('settings.performance.performanceTipsDesc')}
          type="info"
          showIcon
          className="rounded-lg"
        />

        {/* Action Buttons */}
        <Card className="rounded-lg">
          <div className="flex justify-end">
            <Space>
              <Button size="large">
                {t('common.cancel')}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('settings.performance.saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default PerformanceSettings;
