import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  Switch,
  Space,
  message,
  Table,
  Tag,
  Modal,
  Select,
  Alert,
  Divider
} from 'antd';
import {
  SaveOutlined,
  SecurityScanOutlined,
  LockOutlined,
  KeyOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  MobileOutlined,
  DesktopOutlined,
  TabletOutlined, SafetyOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

const {  Text } = Typography;
const { Password } = Input;
const { Option } = Select;

interface SecurityData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  twoFactorAuth: boolean;
  sessionTimeout: number;
  passwordExpiry: number;
  loginNotifications: boolean;
  ipWhitelist: boolean;
  allowedIPs: string[];
}

interface LoginSession {
  id: string;
  device: string;
  location: string;
  ipAddress: string;
  loginTime: string;
  lastActivity: string;
  status: 'active' | 'expired';
  deviceType: 'desktop' | 'mobile' | 'tablet';
}

const SecuritySettings: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showSessions, setShowSessions] = useState(false);

  // Mock data
  const initialData: SecurityData = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorAuth: true,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginNotifications: true,
    ipWhitelist: false,
    allowedIPs: ['*************', '*********'],
  };

  const mockSessions: LoginSession[] = [
    {
      id: '1',
      device: 'Chrome on Windows',
      location: 'Las Vegas, NV',
      ipAddress: '*************',
      loginTime: '2024-02-24T09:00:00Z',
      lastActivity: '2024-02-24T14:30:00Z',
      status: 'active',
      deviceType: 'desktop',
    },
    {
      id: '2',
      device: 'Safari on iPhone',
      location: 'Las Vegas, NV',
      ipAddress: '*************',
      loginTime: '2024-02-23T15:20:00Z',
      lastActivity: '2024-02-23T18:45:00Z',
      status: 'expired',
      deviceType: 'mobile',
    },
    {
      id: '3',
      device: 'Firefox on MacOS',
      location: 'Henderson, NV',
      ipAddress: '*********',
      loginTime: '2024-02-22T11:15:00Z',
      lastActivity: '2024-02-22T16:20:00Z',
      status: 'expired',
      deviceType: 'desktop',
    },
  ];

  const handleSave = async (values: SecurityData) => {
    setLoading(true);
    try {
      // Validate password change
      if (values.newPassword && values.newPassword !== values.confirmPassword) {
        message.error(t('settings.security.passwordMismatch'));
        return;
      }
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.security.saveSuccess'));
      
      // Clear password fields
      form.setFieldsValue({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      message.error(t('settings.security.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateSession = (sessionId: string) => {
    Modal.confirm({
      title: t('settings.security.terminateSession'),
      content: t('settings.security.terminateSessionConfirm'),
      icon: <ExclamationCircleOutlined />,
      onOk() {
        message.success(t('settings.security.sessionTerminated'));
      },
    });
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <MobileOutlined />;
      case 'tablet':
        return <TabletOutlined />;
      default:
        return <DesktopOutlined />;
    }
  };

  const sessionColumns = [
    {
      title: t('settings.security.device'),
      dataIndex: 'device',
      key: 'device',
      render: (device: string, record: LoginSession) => (
        <div className="flex items-center gap-2">
          {getDeviceIcon(record.deviceType)}
          <div>
            <div className="font-medium">{device}</div>
            <div className="text-sm text-gray-500">{record.location}</div>
          </div>
        </div>
      ),
    },
    {
      title: t('settings.security.ipAddress'),
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      render: (ip: string) => (
        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
          {ip}
        </span>
      ),
    },
    {
      title: t('settings.security.lastActivity'),
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (time: string) => dayjs(time).format('MMM DD, YYYY HH:mm'),
    },
    {
      title: t('settings.security.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {t(`settings.security.sessionStatus.${status}`)}
        </Tag>
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: LoginSession) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => message.info('View session details')}
          />
          {record.status === 'active' && (
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleTerminateSession(record.id)}
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <Form
        form={form}
        layout="vertical"
        initialValues={initialData}
        onFinish={handleSave}
        className="space-y-6"
      >
        {/* Password Change */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <LockOutlined className="text-blue-600" />
              {t('settings.security.changePassword')}
            </span>
          }
          className="rounded-lg"
        >
          <Alert
            message={t('settings.security.passwordRequirements')}
            description={t('settings.security.passwordRequirementsDesc')}
            type="info"
            showIcon
            className="mb-4"
          />
          
          <Row gutter={[24, 16]}>
            <Col xs={24}>
              <Form.Item
                name="currentPassword"
                label={t('settings.security.currentPassword')}
                rules={[{ required: true, message: t('settings.security.currentPasswordRequired') }]}
              >
                <Password size="large" placeholder={t('settings.security.enterCurrentPassword')} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="newPassword"
                label={t('settings.security.newPassword')}
                rules={[
                  { required: true, message: t('settings.security.newPasswordRequired') },
                  { min: 8, message: t('settings.security.passwordMinLength') }
                ]}
              >
                <Password size="large" placeholder={t('settings.security.enterNewPassword')} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="confirmPassword"
                label={t('settings.security.confirmPassword')}
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: t('settings.security.confirmPasswordRequired') },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('settings.security.passwordMismatch')));
                    },
                  }),
                ]}
              >
                <Password size="large" placeholder={t('settings.security.confirmNewPassword')} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Two-Factor Authentication */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <SafetyOutlined className="text-blue-600" />
              {t('settings.security.twoFactorAuth')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.security.enable2FA')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.security.enable2FADesc')}
                </div>
              </div>
              <Form.Item name="twoFactorAuth" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Divider />
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <Text className="font-medium block mb-2">{t('settings.security.authenticatorApp')}</Text>
              <Text className="text-sm text-gray-600 block mb-3">
                {t('settings.security.authenticatorAppDesc')}
              </Text>
              <Button icon={<KeyOutlined />}>
                {t('settings.security.setupAuthenticator')}
              </Button>
            </div>
          </div>
        </Card>

        {/* Session Management */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <SecurityScanOutlined className="text-blue-600" />
              {t('settings.security.sessionManagement')}
            </span>
          }
          className="rounded-lg"
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name="sessionTimeout"
                label={t('settings.security.sessionTimeout')}
                rules={[{ required: true, message: t('settings.security.sessionTimeoutRequired') }]}
              >
                <Select size="large">
                  <Option value={15}>15 {t('settings.security.minutes')}</Option>
                  <Option value={30}>30 {t('settings.security.minutes')}</Option>
                  <Option value={60}>1 {t('settings.security.hour')}</Option>
                  <Option value={120}>2 {t('settings.security.hours')}</Option>
                  <Option value={480}>8 {t('settings.security.hours')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="passwordExpiry"
                label={t('settings.security.passwordExpiry')}
                rules={[{ required: true, message: t('settings.security.passwordExpiryRequired') }]}
              >
                <Select size="large">
                  <Option value={30}>30 {t('settings.security.days')}</Option>
                  <Option value={60}>60 {t('settings.security.days')}</Option>
                  <Option value={90}>90 {t('settings.security.days')}</Option>
                  <Option value={180}>180 {t('settings.security.days')}</Option>
                  <Option value={365}>1 {t('settings.security.year')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Divider />
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.security.loginNotifications')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.security.loginNotificationsDesc')}
                </div>
              </div>
              <Form.Item name="loginNotifications" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.security.activeSessions')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.security.activeSessionsDesc')}
                </div>
              </div>
              <Button onClick={() => setShowSessions(true)}>
                {t('settings.security.manageSessions')}
              </Button>
            </div>
          </div>
        </Card>

        {/* IP Whitelist */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <SecurityScanOutlined className="text-blue-600" />
              {t('settings.security.ipWhitelist')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.security.enableIPWhitelist')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.security.enableIPWhitelistDesc')}
                </div>
              </div>
              <Form.Item name="ipWhitelist" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="allowedIPs"
              label={t('settings.security.allowedIPs')}
              dependencies={['ipWhitelist']}
            >
              <Select
                mode="tags"
                size="large"
                placeholder={t('settings.security.enterIPAddresses')}
                disabled={!form.getFieldValue('ipWhitelist')}
                className="w-full"
              />
            </Form.Item>
          </div>
        </Card>

        {/* Action Buttons */}
        <Card className="rounded-lg">
          <div className="flex justify-end">
            <Space>
              <Button size="large">
                {t('common.cancel')}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('settings.security.saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>

      {/* Sessions Modal */}
      <Modal
        title={t('settings.security.activeSessions')}
        open={showSessions}
        onCancel={() => setShowSessions(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={mockSessions}
          columns={sessionColumns}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Modal>
    </div>
  );
};

export default SecuritySettings;
