import React, { useState } from 'react';
import {
  Card,
  Form,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Space,
  message,
  Select,
  TimePicker,
  Tag,
  Alert,
  Modal
} from 'antd';
import {
  SaveOutlined,
  SafetyOutlined,
  CloudDownloadOutlined,
  HistoryOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import GalaxyTable from "@/components/GalaxyTable.tsx";

const {  Text } = Typography;
const { Option } = Select;

interface BackupData {
  autoBackup: boolean;
  backupFrequency: string;
  backupTime: dayjs.Dayjs;
  retentionDays: number;
  includeFiles: boolean;
  includeDatabase: boolean;
  includeSettings: boolean;
  cloudBackup: boolean;
  cloudProvider: string;
}

interface BackupRecord {
  id: string;
  type: 'auto' | 'manual';
  status: 'completed' | 'failed' | 'in_progress';
  size: string;
  date: string;
  duration: string;
  includes: string[];
}

const BackupSettings: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [backupInProgress, setBackupInProgress] = useState(false);

  const initialData: BackupData = {
    autoBackup: true,
    backupFrequency: 'daily',
    backupTime: dayjs('02:00'),
    retentionDays: 30,
    includeFiles: true,
    includeDatabase: true,
    includeSettings: true,
    cloudBackup: false,
    cloudProvider: 'aws',
  };

  const mockBackups: BackupRecord[] = [
    {
      id: '1',
      type: 'auto',
      status: 'completed',
      size: '2.4 GB',
      date: '2024-02-24T02:00:00Z',
      duration: '12 min',
      includes: ['database', 'files', 'settings'],
    },
    {
      id: '2',
      type: 'manual',
      status: 'completed',
      size: '2.3 GB',
      date: '2024-02-23T15:30:00Z',
      duration: '10 min',
      includes: ['database', 'settings'],
    },
    {
      id: '3',
      type: 'auto',
      status: 'failed',
      size: '-',
      date: '2024-02-23T02:00:00Z',
      duration: '2 min',
      includes: ['database', 'files', 'settings'],
    },
  ];
  //@ts-expect-error
  const handleSave = async (values: BackupData) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.backup.saveSuccess'));
    } catch (error) {
      message.error(t('settings.backup.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleManualBackup = async () => {
    setBackupInProgress(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      message.success(t('settings.backup.backupSuccess'));
    } catch (error) {
      message.error(t('settings.backup.backupError'));
    } finally {
      setBackupInProgress(false);
    }
  };
  //@ts-expect-error
  const handleDeleteBackup = (backupId: string) => {
    Modal.confirm({
      title: t('settings.backup.deleteBackup'),
      content: t('settings.backup.deleteBackupConfirm'),
      icon: <ExclamationCircleOutlined />,
      onOk() {
        message.success(t('settings.backup.backupDeleted'));
      },
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className="text-green-600" />;
      case 'failed':
        return <ExclamationCircleOutlined className="text-red-600" />;
      case 'in_progress':
        return <ClockCircleOutlined className="text-blue-600" />;
      default:
        return null;
    }
  };

  const backupColumns = [
    {
      title: t('settings.backup.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'auto' ? 'blue' : 'green'}>
          {t(`settings.backup.types.${type}`)}
        </Tag>
      ),
    },
    {
      title: t('settings.backup.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(status)}
          <span>{t(`settings.backup.statuses.${status}`)}</span>
        </div>
      ),
    },
    {
      title: t('settings.backup.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('MMM DD, YYYY HH:mm'),
    },
    {
      title: t('settings.backup.size'),
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: t('settings.backup.duration'),
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: BackupRecord) => (
        <Space>
          {record.status === 'completed' && (
            <Button
              type="text"
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => message.info('Download backup')}
            />
          )}
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDeleteBackup(record.id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Backup Status */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <SafetyOutlined className="text-blue-600" />
            {t('settings.backup.backupStatus')}
          </span>
        }
        className="rounded-lg"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={8}>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">15</div>
              <div className="text-sm text-green-700">{t('settings.backup.totalBackups')}</div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">2.4 GB</div>
              <div className="text-sm text-blue-700">{t('settings.backup.lastBackupSize')}</div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">98.5%</div>
              <div className="text-sm text-purple-700">{t('settings.backup.successRate')}</div>
            </div>
          </Col>
        </Row>
        
        <div className="mt-4 flex justify-center">
          <Button
            type="primary"
            icon={<CloudDownloadOutlined />}
            onClick={handleManualBackup}
            loading={backupInProgress}
            size="large"
            className="bg-blue-600 hover:bg-blue-700"
          >
            {t('settings.backup.createBackup')}
          </Button>
        </div>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialData}
        onFinish={handleSave}
        className="space-y-6"
      >
        {/* Automatic Backup */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <HistoryOutlined className="text-blue-600" />
              {t('settings.backup.automaticBackup')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.backup.enableAutoBackup')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.backup.enableAutoBackupDesc')}
                </div>
              </div>
              <Form.Item name="autoBackup" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Row gutter={[24, 16]}>
              <Col xs={24} lg={8}>
                <Form.Item
                  name="backupFrequency"
                  label={t('settings.backup.frequency')}
                  dependencies={['autoBackup']}
                >
                  <Select 
                    size="large"
                    disabled={!form.getFieldValue('autoBackup')}
                  >
                    <Option value="hourly">{t('settings.backup.frequencies.hourly')}</Option>
                    <Option value="daily">{t('settings.backup.frequencies.daily')}</Option>
                    <Option value="weekly">{t('settings.backup.frequencies.weekly')}</Option>
                    <Option value="monthly">{t('settings.backup.frequencies.monthly')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} lg={8}>
                <Form.Item
                  name="backupTime"
                  label={t('settings.backup.backupTime')}
                  dependencies={['autoBackup']}
                >
                  <TimePicker 
                    size="large" 
                    format="HH:mm" 
                    className="w-full"
                    disabled={!form.getFieldValue('autoBackup')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} lg={8}>
                <Form.Item
                  name="retentionDays"
                  label={t('settings.backup.retentionDays')}
                  dependencies={['autoBackup']}
                >
                  <Select 
                    size="large"
                    disabled={!form.getFieldValue('autoBackup')}
                  >
                    <Option value={7}>7 {t('settings.backup.days')}</Option>
                    <Option value={14}>14 {t('settings.backup.days')}</Option>
                    <Option value={30}>30 {t('settings.backup.days')}</Option>
                    <Option value={90}>90 {t('settings.backup.days')}</Option>
                    <Option value={365}>1 {t('settings.backup.year')}</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Card>

        {/* Backup Content */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <SafetyOutlined className="text-blue-600" />
              {t('settings.backup.backupContent')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.backup.includeDatabase')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.backup.includeDatabaseDesc')}
                </div>
              </div>
              <Form.Item name="includeDatabase" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.backup.includeFiles')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.backup.includeFilesDesc')}
                </div>
              </div>
              <Form.Item name="includeFiles" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.backup.includeSettings')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.backup.includeSettingsDesc')}
                </div>
              </div>
              <Form.Item name="includeSettings" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
          </div>
        </Card>

        {/* Cloud Backup */}
        <Card 
          title={
            <span className="flex items-center gap-2">
              <CloudDownloadOutlined className="text-blue-600" />
              {t('settings.backup.cloudBackup')}
            </span>
          }
          className="rounded-lg"
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t('settings.backup.enableCloudBackup')}</Text>
                <div className="text-sm text-gray-500">
                  {t('settings.backup.enableCloudBackupDesc')}
                </div>
              </div>
              <Form.Item name="cloudBackup" valuePropName="checked" className="mb-0">
                <Switch />
              </Form.Item>
            </div>
            
            <Form.Item
              name="cloudProvider"
              label={t('settings.backup.cloudProvider')}
              dependencies={['cloudBackup']}
            >
              <Select 
                size="large"
                disabled={!form.getFieldValue('cloudBackup')}
              >
                <Option value="aws">Amazon S3</Option>
                <Option value="gcp">Google Cloud Storage</Option>
                <Option value="azure">Azure Blob Storage</Option>
                <Option value="dropbox">Dropbox</Option>
              </Select>
            </Form.Item>
            
            <Alert
              message={t('settings.backup.cloudWarning')}
              description={t('settings.backup.cloudWarningDesc')}
              type="info"
              showIcon
            />
          </div>
        </Card>

        {/* Action Buttons */}
        <Card className="rounded-lg">
          <div className="flex justify-end">
            <Space>
              <Button size="large">
                {t('common.cancel')}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('settings.backup.saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>

      {/* Backup History */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <HistoryOutlined className="text-blue-600" />
            {t('settings.backup.backupHistory')}
          </span>
        }
        className="rounded-lg"
      >
        <GalaxyTable
          data={mockBackups}
          columns={backupColumns}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} backups`,
            current: 1,
            total: 10,
            onChange: (page, pageSize) => {
              console.log('Page:', page, 'PageSize:', pageSize);
            },
          }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default BackupSettings;
