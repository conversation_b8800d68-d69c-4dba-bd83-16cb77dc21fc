import React, { useState } from 'react';
import {
  Card,
  Radio,
  Button,
  Typography,
  Space,
  message,
  Row,
  Col,
  Avatar,
  Progress
} from 'antd';
import {
  SaveOutlined,
  GlobalOutlined,
  CheckOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  progress: number;
  isRTL?: boolean;
}

const LanguageSettings: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const languages: Language[] = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      progress: 100,
    },
    {
      code: 'tw',
      name: 'Traditional Chinese',
      nativeName: '繁體中文',
      flag: '🇹🇼',
      progress: 100,
    },
    {
      code: 'cn',
      name: 'Simplified Chinese',
      nativeName: '简体中文',
      flag: '🇨🇳',
      progress: 95,
    },
    {
      code: 'ja',
      name: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
      progress: 85,
    },
    {
      code: 'ko',
      name: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
      progress: 80,
    },
    {
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
      progress: 75,
    },
    {
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
      progress: 70,
    },
    {
      code: 'de',
      name: 'German',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
      progress: 65,
    },
  ];

  const handleSave = async () => {
    setLoading(true);
    try {
      await i18n.changeLanguage(selectedLanguage);
      // Mock API call to save preference
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('settings.language.saveSuccess'));
    } catch (error) {
      message.error(t('settings.language.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 95) return '#52c41a';
    if (progress >= 80) return '#faad14';
    if (progress >= 60) return '#ff7a45';
    return '#ff4d4f';
  };

  return (
    <div className="space-y-6">
      {/* Current Language */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <GlobalOutlined className="text-blue-600" />
            {t('settings.language.currentLanguage')}
          </span>
        }
        className="rounded-lg"
      >
        <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <Avatar size={48} className="text-2xl">
            {languages.find(lang => lang.code === i18n.language)?.flag}
          </Avatar>
          <div className="flex-1">
            <Title level={4} className="mb-1">
              {languages.find(lang => lang.code === i18n.language)?.name}
            </Title>
            <Text className="text-gray-600">
              {languages.find(lang => lang.code === i18n.language)?.nativeName}
            </Text>
          </div>
          <CheckOutlined className="text-blue-600 text-xl" />
        </div>
      </Card>

      {/* Language Selection */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <GlobalOutlined className="text-blue-600" />
            {t('settings.language.selectLanguage')}
          </span>
        }
        className="rounded-lg"
      >
        <Text className="text-gray-600 block mb-4">
          {t('settings.language.selectLanguageDesc')}
        </Text>
        
        <Radio.Group 
          value={selectedLanguage} 
          onChange={(e) => setSelectedLanguage(e.target.value)}
          className="w-full"
        >
          <Row gutter={[16, 16]}>
            {languages.map((language) => (
              <Col xs={24} sm={12} lg={8} key={language.code}>
                <Radio.Button 
                  value={language.code} 
                  className="w-full h-auto p-0 border-0"
                >
                  <div className={`p-4 rounded-lg border-2 transition-all ${
                    selectedLanguage === language.code 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center gap-3 mb-3">
                      <Avatar size={40} className="text-xl">
                        {language.flag}
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {language.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {language.nativeName}
                        </div>
                      </div>
                      {selectedLanguage === language.code && (
                        <CheckOutlined className="text-blue-600" />
                      )}
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-500">
                          {t('settings.language.translationProgress')}
                        </span>
                        <span className="font-medium">
                          {language.progress}%
                        </span>
                      </div>
                      <Progress 
                        percent={language.progress} 
                        size="small" 
                        strokeColor={getProgressColor(language.progress)}
                        showInfo={false}
                      />
                    </div>
                  </div>
                </Radio.Button>
              </Col>
            ))}
          </Row>
        </Radio.Group>
      </Card>

      {/* Regional Settings */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <GlobalOutlined className="text-blue-600" />
            {t('settings.language.regionalSettings')}
          </span>
        }
        className="rounded-lg"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <div className="p-4 border rounded-lg">
              <Title level={5} className="mb-2">
                {t('settings.language.dateFormat')}
              </Title>
              <Text className="text-gray-600 block mb-2">
                {t('settings.language.dateFormatDesc')}
              </Text>
              <div className="bg-gray-50 p-2 rounded text-sm font-mono">
                {new Date().toLocaleDateString(selectedLanguage)}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div className="p-4 border rounded-lg">
              <Title level={5} className="mb-2">
                {t('settings.language.timeFormat')}
              </Title>
              <Text className="text-gray-600 block mb-2">
                {t('settings.language.timeFormatDesc')}
              </Text>
              <div className="bg-gray-50 p-2 rounded text-sm font-mono">
                {new Date().toLocaleTimeString(selectedLanguage)}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div className="p-4 border rounded-lg">
              <Title level={5} className="mb-2">
                {t('settings.language.numberFormat')}
              </Title>
              <Text className="text-gray-600 block mb-2">
                {t('settings.language.numberFormatDesc')}
              </Text>
              <div className="bg-gray-50 p-2 rounded text-sm font-mono">
                {(1234567.89).toLocaleString(selectedLanguage)}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div className="p-4 border rounded-lg">
              <Title level={5} className="mb-2">
                {t('settings.language.currencyFormat')}
              </Title>
              <Text className="text-gray-600 block mb-2">
                {t('settings.language.currencyFormatDesc')}
              </Text>
              <div className="bg-gray-50 p-2 rounded text-sm font-mono">
                {(1234.56).toLocaleString(selectedLanguage, {
                  style: 'currency',
                  currency: 'USD'
                })}
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Translation Status */}
      <Card 
        title={
          <span className="flex items-center gap-2">
            <GlobalOutlined className="text-blue-600" />
            {t('settings.language.translationStatus')}
          </span>
        }
        className="rounded-lg"
      >
        <Text className="text-gray-600 block mb-4">
          {t('settings.language.translationStatusDesc')}
        </Text>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {languages.slice(0, 4).map((language) => (
            <div key={language.code} className="text-center p-4 border rounded-lg">
              <Avatar size={32} className="mb-2">
                {language.flag}
              </Avatar>
              <div className="font-medium text-sm mb-1">
                {language.name}
              </div>
              <Progress 
                type="circle" 
                percent={language.progress} 
                size={60}
                strokeColor={getProgressColor(language.progress)}
              />
            </div>
          ))}
        </div>
      </Card>

      {/* Action Buttons */}
      <Card className="rounded-lg">
        <div className="flex justify-between items-center">
          <Text className="text-gray-600">
            {t('settings.language.changeNote')}
          </Text>
          
          <Space>
            <Button size="large">
              {t('common.cancel')}
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              size="large"
              className="bg-blue-600 hover:bg-blue-700"
              disabled={selectedLanguage === i18n.language}
            >
              {t('settings.language.applyLanguage')}
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default LanguageSettings;
