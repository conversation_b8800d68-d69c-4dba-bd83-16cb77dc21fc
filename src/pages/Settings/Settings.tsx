import React, { useState } from 'react';
import { 
  Tabs, 
  Card, 
  Typography, 
  Alert
} from 'antd';
import {
  SettingOutlined,
  BellOutlined,
  DatabaseOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import GeneralSettings from './components/GeneralSettings';
import NotificationSettings from './components/NotificationSettings';
import SystemSettings from './components/SystemSettings';
import BackupSettings from './components/BackupSettings';
// import PerformanceSettings from './components/PerformanceSettings';

const { Title, Text } = Typography;

const Settings: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('general');

  const tabItems = [
    {
      key: 'general',
      label: (
        <span className="flex items-center gap-2">
          <SettingOutlined />
          {t('settings.tabs.general')}
        </span>
      ),
      children: <GeneralSettings />,
    },
    {
      key: 'notifications',
      label: (
        <span className="flex items-center gap-2">
          <BellOutlined />
          {t('settings.tabs.notifications')}
        </span>
      ),
      children: <NotificationSettings />,
    },
    {
      key: 'system',
      label: (
        <span className="flex items-center gap-2">
          <DatabaseOutlined />
          {t('settings.tabs.system')}
        </span>
      ),
      children: <SystemSettings />,
    },
    {
      key: 'backup',
      label: (
        <span className="flex items-center gap-2">
          <SafetyOutlined />
          {t('settings.tabs.backup')}
        </span>
      ),
      children: <BackupSettings />,
    },
    // {
    //   key: 'performance',
    //   label: (
    //     <span className="flex items-center gap-2">
    //       <ThunderboltOutlined />
    //       {t('settings.tabs.performance')}
    //     </span>
    //   ),
    //   children: <PerformanceSettings />,
    // },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-3">
          <SettingOutlined className="text-blue-600 text-2xl" />
          <div>
            <Title level={2} className="text-gray-900 mb-0">
              {t('settings.title')}
            </Title>
            <Text className="text-gray-500">
              {t('settings.subtitle')}
            </Text>
          </div>
        </div>
      </div>

      {/* System Status Alert */}
      <Alert
        message={t('settings.systemStatus.title')}
        description={t('settings.systemStatus.description')}
        type="success"
        showIcon
        className="rounded-lg"
      />

      {/* Settings Tabs */}
      <Card className="rounded-2xl border-0 shadow-card">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          className="settings-tabs"
          tabPosition="top"
        />
      </Card>
    </div>
  );
};

export default Settings;
