import React, { useState } from 'react';
import { Tabs, Card, Row, Col, Statistic, Typography, Alert, Collapse } from 'antd';
import {
  TeamOutlined,
  ShopOutlined,
  WarningOutlined,
  UserOutlined,
  BankOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useManagementStats } from '@/hooks/useManagement.ts';
import EmployeesManagement from '@/pages/Management/components/EmployeesManagement';
import FrontDeskManagement from '@/pages/Management/components/FrontDeskManagement';
import StoresManagement from '@/pages/Management/components/StoresManagement';
import RateManagement from '@/pages/Management/components/RateManagement/RateManagement';

const { Title, Text } = Typography;

const Management: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [activeTab, setActiveTab] = useState('employees');

  const { data: statsResponse, isLoading: statsLoading } = useManagementStats();

  // Responsive values
  const cardPadding = useResponsiveValue({
    xs: '16px',
    sm: '20px',
    md: '24px',
    lg: '32px',
    xl: '32px',
    '2xl': '32px'
  });

  const statisticFontSize = useResponsiveValue({
    xs: '20px',
    sm: '24px',
    md: '28px',
    lg: '28px',
    xl: '28px',
    '2xl': '28px'
  });
  const stats = statsResponse?.data;

  const tabItems = [
    {
      key: 'employees',
      label: (
        <span className='flex items-center gap-2'>
          <TeamOutlined />
          {t('management.tabs.employees')}
        </span>
      ),
      children: <EmployeesManagement />,
    },
    {
      key: 'frontDesk',
      label: (
        <span className='flex items-center gap-2'>
          <BankOutlined />
          {t('management.tabs.frontDesk')}
        </span>
      ),
      children: <FrontDeskManagement />,
    },
    {
      key: 'stores',
      label: (
        <span className='flex items-center gap-2'>
          <ShopOutlined />
          {t('management.tabs.stores')}
        </span>
      ),
      children: <StoresManagement />,
    },
    {
      key: 'rates',
      label: (
        <span className='flex items-center gap-2'>
          <DollarOutlined />
          {t('management.tabs.rates')}
        </span>
      ),
      children: <RateManagement />,
    },
  ];

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}>
      {/* Header Section */}
      <div className={`bg-gradient-to-r from-purple-50 to-indigo-50 ${isMobile ? 'rounded-xl p-4' : 'rounded-2xl p-6 lg:p-8'} border border-purple-100`}>
        <div className='flex items-center justify-between'>
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0`}>
              <TeamOutlined className={`text-white ${isMobile ? 'text-lg' : 'text-xl'}`} />
            </div>
            <div className='min-w-0 flex-1'>
              <Title level={2} className={`text-gray-900 mb-1 font-bold ${isMobile ? 'text-lg' : ''}`}>
                {t('management.title')}
              </Title>
              <Text className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
                Comprehensive management system for employees, cash registers,
                and stores
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview - Collapsible */}
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}>
        <Collapse
          ghost
          expandIcon={({ isActive }) => (
            <DownOutlined
              rotate={isActive ? 180 : 0}
              className={`transition-transform duration-200 ${isMobile ? 'text-sm' : ''}`}
            />
          )}
          items={[
            {
              key: 'overview',
              label: (
                <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
                  <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-blue-100 rounded-lg flex items-center justify-center`}>
                    <BarChartOutlined className={`text-blue-600 ${isMobile ? 'text-sm' : ''}`} />
                  </div>
                  <div>
                    <Title level={4} className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-base' : ''}`}>
                      Management Overview
                    </Title>
                    <Text className={`text-gray-600 ${isMobile ? 'text-xs' : ''}`}>
                      Key performance indicators and system status
                    </Text>
                  </div>
                </div>
              ),
              children: (
                <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}>
                  <Row gutter={[{ xs: 12, sm: 16, lg: 24 }, { xs: 12, sm: 16, lg: 24 }]}>
                    <Col xs={12} sm={12} lg={6}>
                      <Card className={`text-center border border-blue-100 bg-blue-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-blue-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.stats.totalEmployees')}
                            </span>
                          }
                          value={stats?.totalEmployees || 0}
                          loading={statsLoading}
                          prefix={<UserOutlined className='text-blue-600' />}
                          valueStyle={{
                            color: '#1d4ed8',
                            fontSize: statisticFontSize
                          }}
                        />
                        <div className='mt-2'>
                          <Text className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600`}>
                            <span className='font-medium'>
                              {stats?.activeEmployees || 0}
                            </span>{' '}
                            active
                          </Text>
                        </div>
                      </Card>
                    </Col>

                    <Col xs={12} sm={12} lg={6}>
                      <Card className={`text-center border border-green-100 bg-green-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-green-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.stats.totalStores')}
                            </span>
                          }
                          value={stats?.totalStores || 0}
                          loading={statsLoading}
                          prefix={<ShopOutlined className='text-green-600' />}
                          valueStyle={{
                            color: '#059669',
                            fontSize: statisticFontSize
                          }}
                        />
                        <div className='mt-2'>
                          <Text className={`${isMobile ? 'text-xs' : 'text-xs'} text-green-600`}>
                            <span className='font-medium'>
                              {stats?.activeStores || 0}
                            </span>{' '}
                            active
                          </Text>
                        </div>
                      </Card>
                    </Col>

                    <Col xs={12} sm={12} lg={6}>
                      <Card className={`text-center border border-purple-100 bg-purple-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-purple-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.stats.openRegisters')}
                            </span>
                          }
                          value={stats?.openRegisters || 0}
                          suffix={`/ ${stats?.totalRegisters || 0}`}
                          loading={statsLoading}
                          prefix={<BankOutlined className='text-purple-600' />}
                          valueStyle={{
                            color: '#7c3aed',
                            fontSize: statisticFontSize
                          }}
                        />
                        <div className='mt-2'>
                          <Text className={`${isMobile ? 'text-xs' : 'text-xs'} text-purple-600`}>
                            {isMobile ? 'Registers' : 'Cash registers operational'}
                          </Text>
                        </div>
                      </Card>
                    </Col>

                    <Col xs={12} sm={12} lg={6}>
                      <Card className={`text-center border border-orange-100 bg-orange-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-orange-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.stats.todayRevenue')}
                            </span>
                          }
                          value={stats?.todayRevenue || 0}
                          precision={2}
                          prefix='$'
                          loading={statsLoading}
                          valueStyle={{
                            color: '#ea580c',
                            fontSize: statisticFontSize
                          }}
                        />
                        <div className='mt-2'>
                          <Text className={`${isMobile ? 'text-xs' : 'text-xs'} text-orange-600`}>
                            {isMobile ? 'Today' : "Today's performance"}
                          </Text>
                        </div>
                      </Card>
                    </Col>
                  </Row>

                  {/* Alert Section */}
                  {stats && (stats.lowStockItems > 0 || stats.pendingShifts > 0) && (
                    <div className={`${isMobile ? 'mt-4' : 'mt-6'} space-y-3`}>
                      {stats.lowStockItems > 0 && (
                        <Alert
                          message={
                            <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                              {stats.lowStockItems} {t('management.stats.lowStockItems')}
                            </span>
                          }
                          description={!isMobile ? 'Some products are running low on stock and need restocking.' : undefined}
                          type='warning'
                          icon={<WarningOutlined />}
                          showIcon
                          className='border-orange-200'
                        />
                      )}

                      {stats.pendingShifts > 0 && (
                        <Alert
                          message={
                            <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                              {stats.pendingShifts} {t('management.stats.pendingShifts')}
                            </span>
                          }
                          description={!isMobile ? 'Some shifts are still active and need to be closed.' : undefined}
                          type='info'
                          icon={<ClockCircleOutlined />}
                          showIcon
                          className='border-blue-200'
                        />
                      )}
                    </div>
                  )}
                </div>
              ),
            },
          ]}
        />
      </Card>

      {/* Management Tabs */}
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size={isMobile ? 'small' : 'large'}
          className='management-tabs'
          tabPosition={isMobile ? 'top' : 'top'}
        />
      </Card>
    </div>
  );
};

export default Management;
