import React, { useState } from 'react';
import { Tabs, Card, Row, Col, Statistic, Typography, Alert } from 'antd';
import {
  TeamOutlined,
  ShopOutlined,
  WarningOutlined,
  UserOutlined,
  BankOutlined,
  ClockCircleOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useManagementStats } from '@/hooks/useManagement.ts';
import EmployeesManagement from '@/pages/Management/components/EmployeesManagement';
import FrontDeskManagement from '@/pages/Management/components/FrontDeskManagement';
import StoresManagement from '@/pages/Management/components/StoresManagement';
import RateManagement from '@/pages/Management/components/RateManagement/RateManagement';

const { Title, Text } = Typography;

const Management: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('employees');

  const { data: statsResponse, isLoading: statsLoading } = useManagementStats();
  const stats = statsResponse?.data;

  const tabItems = [
    {
      key: 'employees',
      label: (
        <span className='flex items-center gap-2'>
          <TeamOutlined />
          {t('management.tabs.employees')}
        </span>
      ),
      children: <EmployeesManagement />,
    },
    {
      key: 'frontDesk',
      label: (
        <span className='flex items-center gap-2'>
          <BankOutlined />
          {t('management.tabs.frontDesk')}
        </span>
      ),
      children: <FrontDeskManagement />,
    },
    {
      key: 'stores',
      label: (
        <span className='flex items-center gap-2'>
          <ShopOutlined />
          {t('management.tabs.stores')}
        </span>
      ),
      children: <StoresManagement />,
    },
    {
      key: 'rates',
      label: (
        <span className='flex items-center gap-2'>
          <DollarOutlined />
          {t('management.tabs.rates')}
        </span>
      ),
      children: <RateManagement />,
    },
  ];

  return (
    <div className='space-y-8'>
      {/* Header Section */}
      <div className='bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8 border border-purple-100'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <div className='w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center'>
              <TeamOutlined className='text-white text-xl' />
            </div>
            <div>
              <Title level={2} className='text-gray-900 mb-1 font-bold'>
                {t('management.title')}
              </Title>
              <Text className='text-gray-600 text-base'>
                Comprehensive management system for employees, cash registers,
                and stores
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <div className='mb-6'>
          <Title level={4} className='text-gray-900 mb-2'>
            Management Overview
          </Title>
          <Text className='text-gray-600'>
            Key performance indicators and system status
          </Text>
        </div>

        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-blue-100 bg-blue-50/50'>
              <Statistic
                title={
                  <span className='text-blue-700 font-medium'>
                    {t('management.stats.totalEmployees')}
                  </span>
                }
                value={stats?.totalEmployees || 0}
                loading={statsLoading}
                prefix={<UserOutlined className='text-blue-600' />}
                valueStyle={{ color: '#1d4ed8' }}
              />
              <div className='mt-2'>
                <Text className='text-xs text-blue-600'>
                  <span className='font-medium'>
                    {stats?.activeEmployees || 0}
                  </span>{' '}
                  active
                </Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-green-100 bg-green-50/50'>
              <Statistic
                title={
                  <span className='text-green-700 font-medium'>
                    {t('management.stats.totalStores')}
                  </span>
                }
                value={stats?.totalStores || 0}
                loading={statsLoading}
                prefix={<ShopOutlined className='text-green-600' />}
                valueStyle={{ color: '#059669' }}
              />
              <div className='mt-2'>
                <Text className='text-xs text-green-600'>
                  <span className='font-medium'>
                    {stats?.activeStores || 0}
                  </span>{' '}
                  active
                </Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-purple-100 bg-purple-50/50'>
              <Statistic
                title={
                  <span className='text-purple-700 font-medium'>
                    {t('management.stats.openRegisters')}
                  </span>
                }
                value={stats?.openRegisters || 0}
                suffix={`/ ${stats?.totalRegisters || 0}`}
                loading={statsLoading}
                prefix={<BankOutlined className='text-purple-600' />}
                valueStyle={{ color: '#7c3aed' }}
              />
              <div className='mt-2'>
                <Text className='text-xs text-purple-600'>
                  Cash registers operational
                </Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-orange-100 bg-orange-50/50'>
              <Statistic
                title={
                  <span className='text-orange-700 font-medium'>
                    {t('management.stats.todayRevenue')}
                  </span>
                }
                value={stats?.todayRevenue || 0}
                precision={2}
                prefix='$'
                loading={statsLoading}
                valueStyle={{ color: '#ea580c' }}
              />
              <div className='mt-2'>
                <Text className='text-xs text-orange-600'>
                  Today's performance
                </Text>
              </div>
            </Card>
          </Col>
        </Row>

        {/* Alert Section */}
        {stats && (stats.lowStockItems > 0 || stats.pendingShifts > 0) && (
          <div className='mt-6 space-y-3'>
            {stats.lowStockItems > 0 && (
              <Alert
                message={
                  <span className='font-medium'>
                    {stats.lowStockItems} {t('management.stats.lowStockItems')}
                  </span>
                }
                description='Some products are running low on stock and need restocking.'
                type='warning'
                icon={<WarningOutlined />}
                showIcon
                className='border-orange-200'
              />
            )}

            {stats.pendingShifts > 0 && (
              <Alert
                message={
                  <span className='font-medium'>
                    {stats.pendingShifts} {t('management.stats.pendingShifts')}
                  </span>
                }
                description='Some shifts are still active and need to be closed.'
                type='info'
                icon={<ClockCircleOutlined />}
                showIcon
                className='border-blue-200'
              />
            )}
          </div>
        )}
      </Card>

      {/* Management Tabs */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size='large'
          className='management-tabs'
        />
      </Card>
    </div>
  );
};

export default Management;
