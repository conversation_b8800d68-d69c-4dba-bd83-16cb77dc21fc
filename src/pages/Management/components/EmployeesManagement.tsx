import React, { useState } from 'react';
import {
  Button,
  Input,
  Select,
  Tag,
  Dropdown,
  Modal,
  Form,
  Row,
  Col,
  Card,
  Progress,
  Statistic,
  Collapse, Typography,
} from 'antd';
import {
  PlusOutlined,
  MoreOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  DollarOutlined,
  EyeOutlined,
  EditOutlined,
  StopOutlined,
  PlayCircleOutlined,
  CalendarOutlined,
  Bar<PERSON>hartOutlined,
  TeamOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import {
  useEmployees,
  useCreateEmployee,
  useUpdateEmployeeStatus,
  useUpdateEmployee,
} from '@/hooks/useManagement.ts';
import { formatDate, formatCurrency } from '@/utils/tableUtils.ts';
import GalaxyTable from '@/components/GalaxyTable';
import EmployeeDetailsModal from '@/pages/Management/components/modals/EmployeeDetailsModal';
import EditEmployeeModal from '@/pages/Management/components/modals/EditEmployeeModal';
import AttendanceModal from '@/pages/Management/components/modals/AttendanceModal';
import type {
  Employee,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
} from '@/types';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const EmployeesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('');
  const [departmentFilter, setDepartmentFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null,
  );
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // Responsive values
  const searchWidth = useResponsiveValue({
    xs: '100%',
    sm: '100%',
    md: '320px',
    lg: '320px',
    xl: '320px',
    '2xl': '320px'
  });

  const selectWidth = useResponsiveValue({
    xs: '100%',
    sm: '48%',
    md: '128px',
    lg: '128px',
    xl: '128px',
    '2xl': '128px'
  });

  const statisticFontSize = useResponsiveValue({
    xs: '18px',
    sm: '20px',
    md: '24px',
    lg: '24px',
    xl: '24px',
    '2xl': '24px'
  });

  const [form] = Form.useForm();
  const createEmployeeMutation = useCreateEmployee();
  const updateStatusMutation = useUpdateEmployeeStatus();
  const updateEmployeeMutation = useUpdateEmployee();

  const { data: employeesResponse, isLoading } = useEmployees({
    page: pagination.current,
    pageSize: pagination.pageSize,
    search: searchTerm,
    role: roleFilter || undefined,
    department: departmentFilter || undefined,
    status: statusFilter || undefined,
  });

  const employees = employeesResponse?.data?.data || [];
  const total = employeesResponse?.data?.total || 0;

  // Calculate enhanced statistics
  const activeEmployees = employees.filter(
    (emp) => emp.status === 'active',
  ).length;
  const inactiveEmployees = employees.filter(
    (emp) => emp.status === 'inactive',
  ).length;
  const suspendedEmployees = employees.filter(
    (emp) => emp.status === 'suspended',
  ).length;
  const avgPerformance =
    employees.length > 0
      ? employees.reduce((sum, emp) => sum + (emp.performanceScore || 0), 0) /
        employees.length
      : 0;
  const avgAttendance =
    employees.length > 0
      ? employees.reduce((sum, emp) => sum + (emp.attendanceRate || 0), 0) /
        employees.length
      : 0;
  const totalSalary = employees.reduce((sum, emp) => sum + emp.salary, 0);

  // Department breakdown
  const departmentCounts = employees.reduce(
    (acc, emp) => {
      acc[emp.department] = (acc[emp.department] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  // Role breakdown
  const roleCounts = employees.reduce(
    (acc, emp) => {
      acc[emp.role] = (acc[emp.role] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handleFilterChange = (type: string, value: string) => {
    switch (type) {
      case 'role':
        setRoleFilter(value);
        break;
      case 'department':
        setDepartmentFilter(value);
        break;
      case 'status':
        setStatusFilter(value);
        break;
    }
    setPagination({ ...pagination, current: 1 });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleAddEmployee = async (values: CreateEmployeeRequest) => {
    try {
      await createEmployeeMutation.mutateAsync(values);
      setShowAddForm(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to create employee:', error);
    }
  };

  const handleViewEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowDetailsModal(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowEditModal(true);
  };

  const handleViewAttendance = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowAttendanceModal(true);
  };

  const handleUpdateEmployee = async (
    employeeId: string,
    data: UpdateEmployeeRequest,
  ) => {
    try {
      await updateEmployeeMutation.mutateAsync({ id: employeeId, data });
      setShowEditModal(false);
      setSelectedEmployee(null);
    } catch (error) {
      console.error('Failed to update employee:', error);
    }
  };

  // const handleToggleStatus = async (employee: Employee) => {
  //   const newStatus = employee.status === 'active' ? 'inactive' : 'active';
  //   try {
  //     await updateStatusMutation.mutateAsync({ id: employee.id, status: newStatus });
  //   } catch (error) {
  //     console.error('Failed to update employee status:', error);
  //   }
  // };

  const handleStatusChange = async (
    employeeId: string,
    newStatus: Employee['status'],
  ) => {
    try {
      await updateStatusMutation.mutateAsync({
        id: employeeId,
        status: newStatus,
      });
    } catch (error) {
      console.error('Failed to update employee status:', error);
    }
  };

  const getStatusColor = (status: Employee['status']) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'suspended':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getRoleColor = (role: Employee['role']) => {
    switch (role) {
      case 'admin':
        return 'purple';
      case 'manager':
        return 'blue';
      case 'cashier':
        return 'green';
      case 'security':
        return 'orange';
      case 'maintenance':
        return 'gray';
      default:
        return 'default';
    }
  };

  const getActionMenuItems = (employee: Employee) => [
    {
      key: 'view',
      label: t('common.view'),
      icon: <EyeOutlined />,
      onClick: () => handleViewEmployee(employee),
    },
    {
      key: 'edit',
      label: t('common.edit'),
      icon: <EditOutlined />,
      onClick: () => handleEditEmployee(employee),
    },
    {
      key: 'attendance',
      label: t('management.employees.attendance'),
      icon: <CalendarOutlined />,
      onClick: () => handleViewAttendance(employee),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'activate',
      label: t('management.employees.activate'),
      disabled: employee.status === 'active',
      icon: <PlayCircleOutlined />,
      onClick: () => handleStatusChange(employee.id, 'active'),
    },
    {
      key: 'deactivate',
      label: t('management.employees.deactivate'),
      disabled: employee.status === 'inactive',
      onClick: () => handleStatusChange(employee.id, 'inactive'),
      icon: <StopOutlined />,
    },
    {
      key: 'suspend',
      label: 'Suspend',
      disabled: employee.status === 'suspended',
      onClick: () => handleStatusChange(employee.id, 'suspended'),
      icon: <StopOutlined />,
    },
  ];

  const columns = [
    {
      title: t('management.employees.name'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Employee) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0`}>
            <UserOutlined className={`text-white ${isMobile ? 'text-sm' : ''}`} />
          </div>
          <div className='min-w-0 flex-1'>
            <div className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''} truncate`}>{name}</div>
            {!isMobile && (
              <div className='text-sm text-gray-500 truncate'>{record.email}</div>
            )}
          </div>
        </div>
      ),
      width: isMobile ? 150 : undefined,
    },
    {
      title: t('management.employees.role'),
      dataIndex: 'role',
      key: 'role',
      render: (role: Employee['role']) => (
        <Tag color={getRoleColor(role)} className={`font-medium ${isMobile ? 'text-xs' : ''}`}>
          {t(`management.employees.roles.${role}`)}
        </Tag>
      ),
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('management.employees.department'),
      dataIndex: 'department',
      key: 'department',
      render: (department: Employee['department']) => (
        <span className={`text-gray-700 ${isMobile ? 'text-sm' : ''}`}>
          {t(`management.employees.departments.${department}`)}
        </span>
      ),
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('management.employees.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: Employee['status']) => (
        <Tag color={getStatusColor(status)} className={`font-medium ${isMobile ? 'text-xs' : ''}`}>
          {t(`management.employees.statuses.${status}`)}
        </Tag>
      ),
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('management.employees.performance'),
      dataIndex: 'performanceScore',
      key: 'performanceScore',
      render: (score: number) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <Progress
            percent={score}
            size='small'
            className={isMobile ? 'w-12' : 'w-16'}
            strokeColor={
              score >= 90 ? '#52c41a' : score >= 70 ? '#faad14' : '#ff4d4f'
            }
          />
          <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium`}>{score}%</span>
        </div>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('management.employees.salary'),
      dataIndex: 'salary',
      key: 'salary',
      render: (salary: number) => (
        <span className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}>
          {formatCurrency(salary)}
        </span>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('management.employees.hireDate'),
      dataIndex: 'hireDate',
      key: 'hireDate',
      render: (date: string) => (
        <span className={`text-gray-600 ${isMobile ? 'text-xs' : ''}`}>{formatDate(date)}</span>
      ),
      responsive: isMobile ? ['xl'] : undefined,
    },
    {
      title: t('management.employees.actions'),
      key: 'actions',
      width: isMobile ? 50 : undefined,
      render: (_: any, record: Employee) => (
        <Dropdown
          menu={{ items: getActionMenuItems(record) }}
          trigger={['click']}
        >
          <Button
            type='text'
            icon={<MoreOutlined />}
            className='hover:bg-gray-100'
            size={isMobile ? 'small' : 'middle'}
          />
        </Dropdown>
      ),
    },
  ];

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6'}`}>
      {/* Controls Section */}
      <div className={`flex ${isMobile ? 'flex-col' : 'flex-col lg:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} justify-between`}>
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} flex-1`}>
          <Search
            placeholder='Search employees...'
            allowClear
            onSearch={handleSearch}
            style={{ width: searchWidth }}
            size={isMobile ? 'middle' : 'large'}
          />

          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'gap-2'}`}>
            <Select
              placeholder='Role'
              allowClear
              value={roleFilter || undefined}
              onChange={(value) => handleFilterChange('role', value || '')}
              style={{ width: selectWidth }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='admin'>
                {t('management.employees.roles.admin')}
              </Option>
              <Option value='manager'>
                {t('management.employees.roles.manager')}
              </Option>
              <Option value='cashier'>
                {t('management.employees.roles.cashier')}
              </Option>
              <Option value='security'>
                {t('management.employees.roles.security')}
              </Option>
              <Option value='maintenance'>
                {t('management.employees.roles.maintenance')}
              </Option>
            </Select>

            <Select
              placeholder='Department'
              allowClear
              value={departmentFilter || undefined}
              onChange={(value) =>
                handleFilterChange('department', value || '')
              }
              style={{ width: selectWidth }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='management'>
                {t('management.employees.departments.management')}
              </Option>
              <Option value='gaming'>
                {t('management.employees.departments.gaming')}
              </Option>
              <Option value='security'>
                {t('management.employees.departments.security')}
              </Option>
              <Option value='maintenance'>
                {t('management.employees.departments.maintenance')}
              </Option>
              <Option value='customer_service'>
                {t('management.employees.departments.customer_service')}
              </Option>
            </Select>

            <Select
              placeholder='Status'
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => handleFilterChange('status', value || '')}
              style={{ width: selectWidth }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='active'>
                {t('management.employees.statuses.active')}
              </Option>
              <Option value='inactive'>
                {t('management.employees.statuses.inactive')}
              </Option>
              <Option value='suspended'>
                {t('management.employees.statuses.suspended')}
              </Option>
            </Select>
          </div>
        </div>

        <Button
          type='primary'
          icon={<PlusOutlined />}
          onClick={() => setShowAddForm(true)}
          size={isMobile ? 'middle' : 'large'}
          className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
        >
          {t('management.employees.addEmployee')}
        </Button>
      </div>

      {/* Enhanced Statistics - Collapsible */}
      <Card styles={{body : {padding: isMobile ? '0px' : ''}}} className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}>
        <Collapse
          ghost
          expandIcon={({ isActive }) => (
            <DownOutlined
              rotate={isActive ? 180 : 0}
              className={`transition-transform duration-200 ${isMobile ? 'text-sm' : ''}`}
            />
          )}
          items={[
            {
              key: 'statistics',
              label: (
                <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
                  <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-blue-100 rounded-lg flex items-center justify-center`}>
                    <BarChartOutlined className={`text-blue-600 ${isMobile ? 'text-sm' : ''}`} />
                  </div>
                  <div>
                    <Title level={5} className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-sm' : ''}`}>
                      Employee Statistics
                    </Title>
                    <Text className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      Performance metrics and analytics
                    </Text>
                  </div>
                </div>
              ),
              children: (
                <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}>
                  <Row gutter={[{ xs: 12, sm: 16, lg: 24 }, { xs: 12, sm: 16, lg: 24 }]}>
                    <Col xs={24} sm={12} lg={6}>
                      <Card className={`text-center border border-blue-100 bg-blue-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-blue-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.employees.totalEmployees')}
                            </span>
                          }
                          value={total}
                          prefix={<TeamOutlined className='text-blue-600' />}
                          valueStyle={{
                            color: '#1d4ed8',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                        />
                        <div className='mt-2'>
                          <Progress
                            percent={
                              total > 0 ? Math.round((activeEmployees / total) * 100) : 0
                            }
                            size='small'
                            strokeColor='#1d4ed8'
                            showInfo={false}
                          />
                          <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600 mt-1`}>
                            {Math.round((activeEmployees / total) * 100)}% active
                          </div>
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card className={`text-center border border-green-100 bg-green-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-green-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.employees.avgPerformance')}
                            </span>
                          }
                          value={avgPerformance}
                          precision={1}
                          suffix='%'
                          prefix={<TrophyOutlined className='text-green-600' />}
                          valueStyle={{
                            color: '#059669',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                        />
                        <div className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-green-600`}>
                          {employees.filter((e) => (e.performanceScore || 0) >= 90).length}{' '}
                          {isMobile ? 'high' : t('management.employees.highPerformers')}
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card className={`text-center border border-purple-100 bg-purple-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-purple-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.employees.avgAttendance')}
                            </span>
                          }
                          value={avgAttendance}
                          precision={1}
                          suffix='%'
                          prefix={<ClockCircleOutlined className='text-purple-600' />}
                          valueStyle={{
                            color: '#7c3aed',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                        />
                        <div className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-purple-600`}>
                          {employees.filter((e) => (e.attendanceRate || 0) >= 95).length}{' '}
                          {isMobile ? 'excellent' : t('management.employees.excellentAttendance')}
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={6}>
                      <Card className={`text-center border border-orange-100 bg-orange-50/50 ${isMobile ? 'p-2' : ''}`}>
                        <Statistic
                          title={
                            <span className={`text-orange-700 font-medium ${isMobile ? 'text-xs' : ''}`}>
                              {t('management.employees.totalSalary')}
                            </span>
                          }
                          value={totalSalary}
                          prefix={<DollarOutlined className='text-orange-600' />}
                          precision={0}
                          valueStyle={{
                            color: '#ea580c',
                            fontSize: statisticFontSize,
                            fontWeight: 'bold',
                          }}
                        />
                        <div className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-orange-600`}>
                          {isMobile ? 'Avg' : 'Avg'}: {formatCurrency(totalSalary / (employees.length || 1))}
                        </div>
                      </Card>
                    </Col>
                  </Row>

                  {/* Department & Role Breakdown */}
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={12}>
                      <Card
                        title={
                          <span className='flex items-center gap-2'>
                <BarChartOutlined className='text-blue-600' />
                            {t('management.employees.departmentDistribution')}
              </span>
                        }
                        className='rounded-2xl border-0 shadow-card'
                      >
                        <div className='space-y-3'>
                          {Object.entries(departmentCounts).map(([dept, count]) => (
                            <div key={dept} className='flex items-center justify-between'>
                  <span className='text-gray-700'>
                    {t(`management.employees.departments.${dept}`)}
                  </span>
                              <div className='flex items-center gap-2'>
                                <Progress
                                  percent={Math.round((count / total) * 100)}
                                  size='small'
                                  className='w-20'
                                  showInfo={false}
                                />
                                <span className='font-medium text-blue-600 w-8 text-right'>
                      {count}
                    </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} lg={12}>
                      <Card
                        title={
                          <span className='flex items-center gap-2'>
                <UserOutlined className='text-blue-600' />
                            {t('management.employees.roleDistribution')}
              </span>
                        }
                        className='rounded-2xl border-0 shadow-card'
                      >
                        <div className='space-y-3'>
                          {Object.entries(roleCounts).map(([role, count]) => (
                            <div key={role} className='flex items-center justify-between'>
                  <span className='text-gray-700'>
                    {t(`management.employees.roles.${role}`)}
                  </span>
                              <div className='flex items-center gap-2'>
                                <Progress
                                  percent={Math.round((count / total) * 100)}
                                  size='small'
                                  className='w-20'
                                  showInfo={false}
                                />
                                <span className='font-medium text-blue-600 w-8 text-right'>
                      {count}
                    </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </Card>
                    </Col>
                  </Row>

                  {/* Status Breakdown */}
                  <Row gutter={[24, 24]}>
                    <Col xs={24}>
                      <Card
                        title={
                          <span className='flex items-center gap-2'>
                <CheckCircleOutlined className='text-blue-600' />
                            {t('management.employees.statusOverview')}
              </span>
                        }
                        className='rounded-2xl border-0 shadow-card'
                      >
                        <Row gutter={[16, 16]}>
                          <Col xs={24} sm={8}>
                            <div className='text-center p-4 bg-green-50 rounded-lg border border-green-200'>
                              <div className='text-2xl font-bold text-green-600'>
                                {activeEmployees}
                              </div>
                              <div className='text-sm text-green-700'>
                                {t('management.employees.activeEmployees')}
                              </div>
                              <div className='text-xs text-green-600 mt-1'>
                                {Math.round((activeEmployees / total) * 100)}% of total
                              </div>
                            </div>
                          </Col>
                          <Col xs={24} sm={8}>
                            <div className='text-center p-4 bg-red-50 rounded-lg border border-red-200'>
                              <div className='text-2xl font-bold text-red-600'>
                                {inactiveEmployees}
                              </div>
                              <div className='text-sm text-red-700'>
                                {t('management.employees.inactiveEmployees')}
                              </div>
                              <div className='text-xs text-red-600 mt-1'>
                                {Math.round((inactiveEmployees / total) * 100)}% of total
                              </div>
                            </div>
                          </Col>
                          <Col xs={24} sm={8}>
                            <div className='text-center p-4 bg-orange-50 rounded-lg border border-orange-200'>
                              <div className='text-2xl font-bold text-orange-600'>
                                {suspendedEmployees}
                              </div>
                              <div className='text-sm text-orange-700'>
                                {t('management.employees.suspendedEmployees')}
                              </div>
                              <div className='text-xs text-orange-600 mt-1'>
                                {Math.round((suspendedEmployees / total) * 100)}% of total
                              </div>
                            </div>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </div>
              ),
            },
          ]}
        />
      </Card>


      {/* Employees Table */}
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-sm`}>
        <GalaxyTable
          data={employees}
          columns={columns}
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined,
          }}
          rowKey='id'
          scroll={isMobile ? { x: 1000 } : undefined}
        />
      </Card>

      {/* Add Employee Modal */}
      <Modal
        title={
          <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
            <UserOutlined className='text-blue-600' />
            <span className={isMobile ? 'text-sm' : ''}>{t('management.employees.addEmployee')}</span>
          </div>
        }
        open={showAddForm}
        onCancel={() => {
          setShowAddForm(false);
          form.resetFields();
        }}
        footer={null}
        width={isMobile ? '95%' : 600}
        className={isMobile ? 'top-4' : 'top-8'}
        styles={{
          body: { padding: isMobile ? '16px' : '24px' }
        }}
      >
        <Form
          form={form}
          layout='vertical'
          onFinish={handleAddEmployee}
          className='mt-6'
        >
          <Row gutter={isMobile ? 8 : 16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='name'
                label={t('management.employees.name')}
                rules={[{ required: true, message: 'Name is required' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder='Enter employee name'
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='email'
                label={t('management.employees.email')}
                rules={[
                  { required: true, message: 'Email is required' },
                  { type: 'email', message: 'Invalid email format' },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder='Enter email address'
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={isMobile ? 8 : 16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='phone'
                label={t('management.employees.phone')}
                rules={[{ required: true, message: 'Phone is required' }]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder='Enter phone number'
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='salary'
                label={t('management.employees.salary')}
                rules={[{ required: true, message: 'Salary is required' }]}
              >
                <Input
                  prefix={<DollarOutlined />}
                  type='number'
                  placeholder='Enter salary'
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={isMobile ? 8 : 16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='role'
                label={t('management.employees.role')}
                rules={[{ required: true, message: 'Role is required' }]}
              >
                <Select placeholder='Select role' size={isMobile ? 'middle' : 'large'}>
                  <Option value='admin'>
                    {t('management.employees.roles.admin')}
                  </Option>
                  <Option value='manager'>
                    {t('management.employees.roles.manager')}
                  </Option>
                  <Option value='cashier'>
                    {t('management.employees.roles.cashier')}
                  </Option>
                  <Option value='security'>
                    {t('management.employees.roles.security')}
                  </Option>
                  <Option value='maintenance'>
                    {t('management.employees.roles.maintenance')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='department'
                label={t('management.employees.department')}
                rules={[{ required: true, message: 'Department is required' }]}
              >
                <Select placeholder='Select department' size={isMobile ? 'middle' : 'large'}>
                  <Option value='management'>
                    {t('management.employees.departments.management')}
                  </Option>
                  <Option value='gaming'>
                    {t('management.employees.departments.gaming')}
                  </Option>
                  <Option value='security'>
                    {t('management.employees.departments.security')}
                  </Option>
                  <Option value='maintenance'>
                    {t('management.employees.departments.maintenance')}
                  </Option>
                  <Option value='customer_service'>
                    {t('management.employees.departments.customer_service')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name='permissions'
            label={t('management.employees.permissions')}
            rules={[
              {
                required: true,
                message: 'At least one permission is required',
              },
            ]}
          >
            <Select
              mode='multiple'
              placeholder='Select permissions'
              size={isMobile ? 'middle' : 'large'}
              options={[
                { label: 'All Permissions', value: 'all' },
                { label: 'Manage Employees', value: 'manage_employees' },
                { label: 'View Reports', value: 'view_reports' },
                { label: 'Manage Shifts', value: 'manage_shifts' },
                { label: 'Operate Register', value: 'operate_register' },
                {
                  label: 'Process Transactions',
                  value: 'process_transactions',
                },
                { label: 'Monitor Premises', value: 'monitor_premises' },
                { label: 'Incident Reports', value: 'incident_reports' },
                {
                  label: 'Equipment Maintenance',
                  value: 'equipment_maintenance',
                },
                { label: 'Facility Repairs', value: 'facility_repairs' },
              ]}
            />
          </Form.Item>

          <div className={`flex ${isMobile ? 'flex-col' : 'justify-end'} gap-3 mt-6`}>
            <Button
              onClick={() => {
                setShowAddForm(false);
                form.resetFields();
              }}
              size={isMobile ? 'middle' : 'large'}
              className={isMobile ? 'w-full' : ''}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={createEmployeeMutation.isPending}
              size={isMobile ? 'middle' : 'large'}
              className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
            >
              {t('common.save')}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Employee Details Modal */}
      <EmployeeDetailsModal
        visible={showDetailsModal}
        employee={selectedEmployee}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedEmployee(null);
        }}
      />

      {/* Edit Employee Modal */}
      <EditEmployeeModal
        visible={showEditModal}
        employee={selectedEmployee}
        onClose={() => {
          setShowEditModal(false);
          setSelectedEmployee(null);
        }}
        onSave={handleUpdateEmployee}
        loading={updateEmployeeMutation.isPending}
      />

      {/* Attendance Modal */}
      <AttendanceModal
        visible={showAttendanceModal}
        employee={selectedEmployee}
        onClose={() => {
          setShowAttendanceModal(false);
          setSelectedEmployee(null);
        }}
      />
    </div>
  );
};

export default EmployeesManagement;
