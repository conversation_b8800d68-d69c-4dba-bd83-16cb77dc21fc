import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Button,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  InputNumber,
  TimePicker,
  Alert,
  Dropdown,
} from 'antd';
import {
  ShopOutlined,
  PlusOutlined,
  WarningOutlined,
  TeamOutlined,
  MoreOutlined,
  EditOutlined,
  StockOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  useStores,
  useProducts,
  useCreateStore,
  useUpdateStoreStatus,
  useUpdateProductStock,
  useEmployees,
} from '@/hooks/useManagement.ts';
import { formatDate, formatCurrency } from '@/utils/tableUtils.ts';
import GalaxyTable from '@/components/GalaxyTable';
import type { Store, Product, CreateStoreRequest } from '@/types';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

const StoresManagement: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('stores');
  const [showAddStoreModal, setShowAddStoreModal] = useState(false);
  const [showStockModal, setShowStockModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Filters
  const [storeSearchTerm, setStoreSearchTerm] = useState('');
  const [storeStatusFilter, setStoreStatusFilter] = useState<string>('');
  const [productSearchTerm, setProductSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [lowStockFilter, setLowStockFilter] = useState(false);

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  const [storeForm] = Form.useForm();
  const [stockForm] = Form.useForm();

  // Mutations
  const createStoreMutation = useCreateStore();
  const updateStoreStatusMutation = useUpdateStoreStatus();
  const updateProductStockMutation = useUpdateProductStock();

  // Queries
  const { data: storesResponse, isLoading: storesLoading } = useStores({
    page: pagination.current,
    pageSize: pagination.pageSize,
    search: storeSearchTerm,
    status: storeStatusFilter || undefined,
  });

  const { data: productsResponse, isLoading: productsLoading } = useProducts({
    page: pagination.current,
    pageSize: pagination.pageSize,
    search: productSearchTerm,
    category: categoryFilter || undefined,
    lowStock: lowStockFilter,
  });

  const { data: employeesResponse } = useEmployees({});

  const stores = storesResponse?.data?.data || [];
  const products = productsResponse?.data?.data || [];
  const employees = employeesResponse?.data?.data || [];
  const total =
    storesResponse?.data?.total || productsResponse?.data?.total || 0;

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleCreateStore = async (values: any) => {
    try {
      const storeData: CreateStoreRequest = {
        ...values,
        openingHours: {
          monday: {
            open: values.mondayOpen?.format('HH:mm') || '09:00',
            close: values.mondayClose?.format('HH:mm') || '17:00',
          },
          tuesday: {
            open: values.tuesdayOpen?.format('HH:mm') || '09:00',
            close: values.tuesdayClose?.format('HH:mm') || '17:00',
          },
          wednesday: {
            open: values.wednesdayOpen?.format('HH:mm') || '09:00',
            close: values.wednesdayClose?.format('HH:mm') || '17:00',
          },
          thursday: {
            open: values.thursdayOpen?.format('HH:mm') || '09:00',
            close: values.thursdayClose?.format('HH:mm') || '17:00',
          },
          friday: {
            open: values.fridayOpen?.format('HH:mm') || '09:00',
            close: values.fridayClose?.format('HH:mm') || '17:00',
          },
          saturday: {
            open: values.saturdayOpen?.format('HH:mm') || '09:00',
            close: values.saturdayClose?.format('HH:mm') || '17:00',
          },
          sunday: {
            open: values.sundayOpen?.format('HH:mm') || '09:00',
            close: values.sundayClose?.format('HH:mm') || '17:00',
          },
        },
      };

      await createStoreMutation.mutateAsync(storeData);
      setShowAddStoreModal(false);
      storeForm.resetFields();
    } catch (error) {
      console.error('Failed to create store:', error);
    }
  };

  const handleUpdateStock = async (values: any) => {
    try {
      await updateProductStockMutation.mutateAsync({
        id: selectedProduct!.id,
        newStockLevel: values.newStockLevel,
      });
      setShowStockModal(false);
      stockForm.resetFields();
      setSelectedProduct(null);
    } catch (error) {
      console.error('Failed to update stock:', error);
    }
  };

  const handleStoreStatusChange = async (
    storeId: string,
    newStatus: Store['status'],
  ) => {
    try {
      await updateStoreStatusMutation.mutateAsync({
        id: storeId,
        status: newStatus,
      });
    } catch (error) {
      console.error('Failed to update store status:', error);
    }
  };

  const getStatusColor = (status: Store['status']) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'maintenance':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getStoreActionMenuItems = (store: Store) => [
    {
      key: 'view',
      label: 'View Details',
      icon: <ShopOutlined />,
    },
    {
      key: 'edit',
      label: 'Edit Store',
      icon: <EditOutlined />,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'activate',
      label: 'Activate',
      disabled: store.status === 'active',
      onClick: () => handleStoreStatusChange(store.id, 'active'),
    },
    {
      key: 'deactivate',
      label: 'Deactivate',
      disabled: store.status === 'inactive',
      onClick: () => handleStoreStatusChange(store.id, 'inactive'),
    },
    {
      key: 'maintenance',
      label: 'Set Maintenance',
      disabled: store.status === 'maintenance',
      onClick: () => handleStoreStatusChange(store.id, 'maintenance'),
    },
  ];

  // Store columns
  const storeColumns = [
    {
      title: t('management.stores.storeName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Store) => (
        <div>
          <div className='font-medium text-gray-900'>{name}</div>
          <div className='text-sm text-gray-500'>{record.location}</div>
        </div>
      ),
    },
    {
      title: t('management.stores.manager'),
      dataIndex: 'managerName',
      key: 'managerName',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: Store['status']) => (
        <Tag color={getStatusColor(status)} className='font-medium'>
          {t(`management.stores.storeStatuses.${status}`)}
        </Tag>
      ),
    },
    {
      title: t('management.stores.employeeCount'),
      dataIndex: 'employeeCount',
      key: 'employeeCount',
      render: (count: number) => (
        <div className='flex items-center gap-1'>
          <TeamOutlined className='text-blue-600' />
          <span className='font-medium'>{count}</span>
        </div>
      ),
    },
    {
      title: t('management.stores.monthlyRevenue'),
      dataIndex: 'monthlyRevenue',
      key: 'monthlyRevenue',
      render: (revenue: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(revenue)}
        </span>
      ),
    },
    {
      title: t('management.stores.totalRevenue'),
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      render: (revenue: number) => (
        <span className='font-medium text-blue-600'>
          {formatCurrency(revenue)}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Store) => (
        <Dropdown
          menu={{ items: getStoreActionMenuItems(record) }}
          trigger={['click']}
        >
          <Button
            type='text'
            icon={<MoreOutlined />}
            className='hover:bg-gray-100'
          />
        </Dropdown>
      ),
    },
  ];

  // Product columns
  const productColumns = [
    {
      title: t('management.stores.productName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Product) => (
        <div>
          <div className='font-medium text-gray-900'>{name}</div>
          <div className='text-sm text-gray-500'>{record.category}</div>
        </div>
      ),
    },
    {
      title: 'Store',
      dataIndex: 'storeId',
      key: 'storeId',
      render: (storeId: string) => {
        const store = stores.find((s) => s.id === storeId);
        return store?.name || storeId;
      },
    },
    {
      title: t('management.stores.price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(price)}
        </span>
      ),
    },
    {
      title: t('management.stores.stockLevel'),
      dataIndex: 'stockLevel',
      key: 'stockLevel',
      render: (stock: number, record: Product) => {
        const isLowStock = stock < record.minStockLevel;
        return (
          <div className='flex items-center gap-2'>
            <span
              className={`font-medium ${isLowStock ? 'text-red-600' : 'text-green-600'}`}
            >
              {stock}
            </span>
            {isLowStock && <WarningOutlined className='text-red-500' />}
          </div>
        );
      },
    },
    {
      title: t('management.stores.minStockLevel'),
      dataIndex: 'minStockLevel',
      key: 'minStockLevel',
      render: (min: number) => <span className='text-gray-600'>{min}</span>,
    },
    {
      title: t('management.stores.lastRestocked'),
      dataIndex: 'lastRestocked',
      key: 'lastRestocked',
      render: (date: string) => (date ? formatDate(date) : '-'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Product) => (
        <Button
          type='primary'
          size='small'
          icon={<StockOutlined />}
          onClick={() => {
            setSelectedProduct(record);
            stockForm.setFieldsValue({ newStockLevel: record.stockLevel });
            setShowStockModal(true);
          }}
          className='bg-blue-600 hover:bg-blue-700'
        >
          {t('management.stores.updateStock')}
        </Button>
      ),
    },
  ];

  const tabItems = [
    {
      key: 'stores',
      label: (
        <span className='flex items-center gap-2'>
          <ShopOutlined />
          Stores
        </span>
      ),
      children: (
        <div className='space-y-4'>
          <div className='flex justify-between items-center'>
            <div className='flex gap-4'>
              <Search
                placeholder='Search stores...'
                allowClear
                onSearch={setStoreSearchTerm}
                className='w-80'
                size='large'
              />

              <Select
                placeholder='Filter by status'
                allowClear
                value={storeStatusFilter || undefined}
                onChange={setStoreStatusFilter}
                className='w-40'
                size='large'
              >
                <Option value='active'>
                  {t('management.stores.storeStatuses.active')}
                </Option>
                <Option value='inactive'>
                  {t('management.stores.storeStatuses.inactive')}
                </Option>
                <Option value='maintenance'>
                  {t('management.stores.storeStatuses.maintenance')}
                </Option>
              </Select>
            </div>

            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => setShowAddStoreModal(true)}
              size='large'
              className='bg-blue-600 hover:bg-blue-700'
            >
              {t('management.stores.addStore')}
            </Button>
          </div>

          <GalaxyTable
            data={stores}
            columns={storeColumns}
            loading={storesLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: handlePaginationChange,
            }}
            rowKey='id'
          />
        </div>
      ),
    },
    {
      key: 'inventory',
      label: (
        <span className='flex items-center gap-2'>
          <StockOutlined />
          {t('management.stores.inventory')}
        </span>
      ),
      children: (
        <div className='space-y-4'>
          <div className='flex justify-between items-center'>
            <div className='flex gap-4'>
              <Search
                placeholder='Search products...'
                allowClear
                onSearch={setProductSearchTerm}
                className='w-80'
                size='large'
              />

              <Select
                placeholder='Filter by category'
                allowClear
                value={categoryFilter || undefined}
                onChange={setCategoryFilter}
                className='w-40'
                size='large'
              >
                <Option value='Gaming'>Gaming</Option>
                <Option value='Beverages'>Beverages</Option>
                <Option value='Food'>Food</Option>
                <Option value='Merchandise'>Merchandise</Option>
              </Select>

              <Button
                type={lowStockFilter ? 'primary' : 'default'}
                icon={<WarningOutlined />}
                onClick={() => setLowStockFilter(!lowStockFilter)}
                size='large'
                className={lowStockFilter ? 'bg-red-600 hover:bg-red-700' : ''}
              >
                {t('management.stores.lowStockAlert')}
              </Button>
            </div>
          </div>

          {lowStockFilter && (
            <Alert
              message='Low Stock Items'
              description='Showing products that are below minimum stock level and need restocking.'
              type='warning'
              icon={<WarningOutlined />}
              showIcon
              className='border-orange-200'
            />
          )}

          <GalaxyTable
            data={products}
            columns={productColumns}
            loading={productsLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: handlePaginationChange,
            }}
            rowKey='id'
          />
        </div>
      ),
    },
  ];

  const dayNames = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
  ];

  return (
    <div className='space-y-6'>
      <Card className='rounded-xl border-0 shadow-sm'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size='large'
        />
      </Card>

      {/* Add Store Modal */}
      <Modal
        title={
          <div className='flex items-center gap-2'>
            <ShopOutlined className='text-blue-600' />
            {t('management.stores.addStore')}
          </div>
        }
        open={showAddStoreModal}
        onCancel={() => {
          setShowAddStoreModal(false);
          storeForm.resetFields();
        }}
        footer={null}
        width={800}
        className='top-8'
      >
        <Form
          form={storeForm}
          layout='vertical'
          onFinish={handleCreateStore}
          className='mt-6'
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='name'
                label={t('management.stores.storeName')}
                rules={[{ required: true, message: 'Store name is required' }]}
              >
                <Input
                  prefix={<ShopOutlined />}
                  placeholder='Enter store name'
                  size='large'
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='location'
                label='Location'
                rules={[{ required: true, message: 'Location is required' }]}
              >
                <Input placeholder='Enter location' size='large' />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name='address'
            label={t('management.stores.address')}
            rules={[{ required: true, message: 'Address is required' }]}
          >
            <TextArea placeholder='Enter full address' rows={2} size='large' />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name='phone'
                label='Phone'
                rules={[{ required: true, message: 'Phone is required' }]}
              >
                <Input placeholder='Enter phone number' size='large' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='email'
                label='Email'
                rules={[
                  { required: true, message: 'Email is required' },
                  { type: 'email', message: 'Invalid email format' },
                ]}
              >
                <Input placeholder='Enter email address' size='large' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='managerId'
                label={t('management.stores.manager')}
                rules={[{ required: true, message: 'Manager is required' }]}
              >
                <Select placeholder='Select manager' size='large'>
                  {employees
                    .filter(
                      (emp) => emp.role === 'manager' || emp.role === 'admin',
                    )
                    .map((employee) => (
                      <Option key={employee.id} value={employee.id}>
                        {employee.name} - {employee.role}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <div className='mb-4'>
            <Title level={5}>{t('management.stores.openingHours')}</Title>
            <div className='space-y-3'>
              {dayNames.map((day) => (
                <Row key={day} gutter={16} align='middle'>
                  <Col span={4}>
                    <Text className='font-medium capitalize'>
                      {t(`management.stores.days.${day}`)}
                    </Text>
                  </Col>
                  <Col span={8}>
                    <Form.Item name={`${day}Open`} className='mb-0'>
                      <TimePicker
                        format='HH:mm'
                        placeholder='Open time'
                        size='large'
                        className='w-full'
                        defaultValue={dayjs('09:00', 'HH:mm')}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name={`${day}Close`} className='mb-0'>
                      <TimePicker
                        format='HH:mm'
                        placeholder='Close time'
                        size='large'
                        className='w-full'
                        defaultValue={dayjs('17:00', 'HH:mm')}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </div>
          </div>

          <div className='flex justify-end gap-3 mt-6'>
            <Button
              onClick={() => {
                setShowAddStoreModal(false);
                storeForm.resetFields();
              }}
              size='large'
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={createStoreMutation.isPending}
              size='large'
              className='bg-blue-600 hover:bg-blue-700'
            >
              {t('common.save')}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Update Stock Modal */}
      <Modal
        title={
          <div className='flex items-center gap-2'>
            <StockOutlined className='text-blue-600' />
            {t('management.stores.updateStock')}
          </div>
        }
        open={showStockModal}
        onCancel={() => {
          setShowStockModal(false);
          stockForm.resetFields();
          setSelectedProduct(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={stockForm}
          layout='vertical'
          onFinish={handleUpdateStock}
          className='mt-6'
        >
          {selectedProduct && (
            <div className='mb-4 p-4 bg-gray-50 rounded-lg'>
              <div className='font-medium text-lg'>{selectedProduct.name}</div>
              <div className='text-sm text-gray-600 mt-1'>
                Current Stock:{' '}
                <span className='font-medium'>
                  {selectedProduct.stockLevel}
                </span>
              </div>
              <div className='text-sm text-gray-600'>
                Minimum Stock:{' '}
                <span className='font-medium'>
                  {selectedProduct.minStockLevel}
                </span>
              </div>
            </div>
          )}

          <Form.Item
            name='newStockLevel'
            label='New Stock Level'
            rules={[
              { required: true, message: 'New stock level is required' },
              {
                type: 'number',
                min: 0,
                message: 'Stock level must be positive',
              },
            ]}
          >
            <InputNumber
              placeholder='Enter new stock level'
              size='large'
              className='w-full'
              min={0}
            />
          </Form.Item>

          <div className='flex justify-end gap-3 mt-6'>
            <Button
              onClick={() => {
                setShowStockModal(false);
                stockForm.resetFields();
                setSelectedProduct(null);
              }}
              size='large'
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={updateProductStockMutation.isPending}
              size='large'
              className='bg-blue-600 hover:bg-blue-700'
            >
              {t('management.stores.updateStock')}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default StoresManagement;
