import React from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Avatar,
  Divider,
  Badge,
  Space,
  Progress,
  Table,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  HomeOutlined,
  ContactsOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { formatDate } from '@/utils/tableUtils.ts';
import type { Employee } from '@/types';

const { Title, Text } = Typography;

interface EmployeeDetailsModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;
}

const EmployeeDetailsModal: React.FC<EmployeeDetailsModalProps> = ({
  visible,
  employee,
  onClose,
}) => {
  const { t } = useTranslation();

  if (!employee) return null;
  const getRoleColor = (role: Employee['role']) => {
    switch (role) {
      case 'admin':
        return 'red';
      case 'manager':
        return 'blue';
      case 'cashier':
        return 'green';
      case 'security':
        return 'orange';
      case 'maintenance':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  const scheduleColumns = [
    {
      title: t('management.employees.day'),
      dataIndex: 'day',
      key: 'day',
    },
    {
      title: t('management.employees.startTime'),
      dataIndex: 'start',
      key: 'start',
      render: (start: string, record: any) =>
        record.off ? (
          <Tag color='red'>{t('management.employees.dayOff')}</Tag>
        ) : (
          <span>{start}</span>
        ),
    },
    {
      title: t('management.employees.endTime'),
      dataIndex: 'end',
      key: 'end',
      render: (end: string, record: any) =>
        record.off ? (
          <Tag color='red'>{t('management.employees.dayOff')}</Tag>
        ) : (
          <span>{end}</span>
        ),
    },
    {
      title: t('management.employees.hours'),
      key: 'hours',
      render: (_: any, record: any) => {
        if (record.off) return '-';
        const start = new Date(`2024-01-01 ${record.start}`);
        const end = new Date(`2024-01-01 ${record.end}`);
        let hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        if (hours < 0) hours += 24; // Handle overnight shifts
        return `${hours.toFixed(1)}h`;
      },
    },
  ];

  const scheduleData = employee.schedule
    ? [
        {
          key: 'monday',
          day: t('management.employees.days.monday'),
          ...employee.schedule.monday,
        },
        {
          key: 'tuesday',
          day: t('management.employees.days.tuesday'),
          ...employee.schedule.tuesday,
        },
        {
          key: 'wednesday',
          day: t('management.employees.days.wednesday'),
          ...employee.schedule.wednesday,
        },
        {
          key: 'thursday',
          day: t('management.employees.days.thursday'),
          ...employee.schedule.thursday,
        },
        {
          key: 'friday',
          day: t('management.employees.days.friday'),
          ...employee.schedule.friday,
        },
        {
          key: 'saturday',
          day: t('management.employees.days.saturday'),
          ...employee.schedule.saturday,
        },
        {
          key: 'sunday',
          day: t('management.employees.days.sunday'),
          ...employee.schedule.sunday,
        },
      ]
    : [];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className='top-8'
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
    >
      <div className='p-6'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center gap-4'>
            <Avatar
              size={64}
              className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-xl'
            >
              {employee.name.charAt(0).toUpperCase()}
            </Avatar>
            <div>
              <div className='flex items-center gap-3'>
                <Title level={3} className='text-gray-900 mb-0 font-bold'>
                  {employee.name}
                </Title>
                <Badge
                  status={employee.status === 'active' ? 'success' : 'error'}
                  text={
                    <span
                      className={`font-medium ${employee.status === 'active' ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {t(`management.employees.statuses.${employee.status}`)}
                    </span>
                  }
                />
              </div>
              <div className='flex items-center gap-2 mt-1'>
                <Tag
                  color={getRoleColor(employee.role)}
                  className='font-medium'
                >
                  {t(`management.employees.roles.${employee.role}`)}
                </Tag>
                <Text className='text-gray-600'>
                  {t(`management.employees.departments.${employee.department}`)}
                </Text>
              </div>
            </div>
          </div>
        </div>

        <Divider className='my-6' />

        {/* Performance Statistics */}
        <Row gutter={[16, 16]} className='mb-6'>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-blue-100 bg-blue-50/50'>
              <Statistic
                title={t('management.employees.performanceScore')}
                value={employee.performanceScore || 0}
                suffix='%'
                valueStyle={{
                  color: getPerformanceColor(employee.performanceScore || 0),
                  fontSize: '20px',
                }}
              />
              <Progress
                percent={employee.performanceScore || 0}
                size='small'
                strokeColor={getPerformanceColor(
                  employee.performanceScore || 0,
                )}
                showInfo={false}
                className='mt-2'
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-green-100 bg-green-50/50'>
              <Statistic
                title={t('management.employees.attendanceRate')}
                value={employee.attendanceRate || 0}
                suffix='%'
                prefix={<CheckCircleOutlined className='text-green-600' />}
                valueStyle={{ color: '#059669', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-purple-100 bg-purple-50/50'>
              <Statistic
                title={t('management.employees.salary')}
                value={employee.salary}
                prefix='$'
                precision={0}
                valueStyle={{ color: '#7c3aed', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className='text-center border border-orange-100 bg-orange-50/50'>
              <Statistic
                title={t('management.employees.experience')}
                value={
                  Math.floor(
                    (new Date().getTime() -
                      new Date(employee.hireDate).getTime()) /
                      (1000 * 60 * 60 * 24 * 365 * 100),
                  ) / 100
                }
                suffix='years'
                prefix={<TrophyOutlined className='text-orange-600' />}
                valueStyle={{ color: '#ea580c', fontSize: '20px' }}
                precision={1}
              />
            </Card>
          </Col>
        </Row>

        {/* Employee Information */}
        <Row gutter={[24, 24]} className='mb-6'>
          <Col xs={24} lg={12}>
            <Card
              title={
                <span className='flex items-center gap-2'>
                  <UserOutlined className='text-blue-600' />
                  {t('management.employees.personalInfo')}
                </span>
              }
            >
              <Descriptions column={1} size='middle'>
                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <MailOutlined className='text-gray-500' />
                      {t('management.employees.email')}
                    </span>
                  }
                >
                  <a
                    href={`mailto:${employee.email}`}
                    className='text-blue-600 hover:text-blue-800'
                  >
                    {employee.email}
                  </a>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <PhoneOutlined className='text-gray-500' />
                      {t('management.employees.phone')}
                    </span>
                  }
                >
                  <a
                    href={`tel:${employee.phone}`}
                    className='text-blue-600 hover:text-blue-800'
                  >
                    {employee.phone}
                  </a>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <CalendarOutlined className='text-gray-500' />
                      {t('management.employees.hireDate')}
                    </span>
                  }
                >
                  {formatDate(employee.hireDate)}
                </Descriptions.Item>

                {employee.address && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <HomeOutlined className='text-gray-500' />
                        {t('management.employees.address')}
                      </span>
                    }
                  >
                    {employee.address}
                  </Descriptions.Item>
                )}

                {employee.lastLogin && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <ClockCircleOutlined className='text-gray-500' />
                        {t('management.employees.lastLogin')}
                      </span>
                    }
                  >
                    {formatDate(employee.lastLogin)}
                  </Descriptions.Item>
                )}
              </Descriptions>

              {employee.emergencyContact && (
                <div className='mt-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <ContactsOutlined className='text-gray-500' />
                    <Text className='font-medium text-gray-700'>
                      {t('management.employees.emergencyContact')}
                    </Text>
                  </div>
                  <div className='ml-6'>
                    <div>{employee.emergencyContact.name}</div>
                    <div className='text-sm text-gray-600'>
                      {employee.emergencyContact.phone}
                    </div>
                    <div className='text-xs text-gray-500'>
                      {employee.emergencyContact.relationship}
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            {/* Performance Metrics */}
            {employee.performanceMetrics && (
              <Card
                title={
                  <span className='flex items-center gap-2'>
                    <StarOutlined className='text-blue-600' />
                    {t('management.employees.performanceMetrics')}
                  </span>
                }
                className='mb-4'
              >
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Statistic
                      title={t('management.employees.tasksCompleted')}
                      value={employee.performanceMetrics.tasksCompleted}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('management.employees.customerSatisfaction')}
                      value={employee.performanceMetrics.customerSatisfaction}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('management.employees.punctuality')}
                      value={employee.performanceMetrics.punctuality}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('management.employees.teamwork')}
                      value={employee.performanceMetrics.teamwork}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                </Row>

                <Divider />

                <div className='mb-4'>
                  <Text className='font-medium text-gray-700 block mb-2'>
                    {t('management.employees.goals')}
                  </Text>
                  {employee.performanceMetrics.goals.map((goal, index) => (
                    <div key={index} className='flex items-center gap-2 mb-1'>
                      <CheckCircleOutlined className='text-blue-500 text-xs' />
                      <Text className='text-sm'>{goal}</Text>
                    </div>
                  ))}
                </div>

                <div>
                  <Text className='font-medium text-gray-700 block mb-2'>
                    {t('management.employees.achievements')}
                  </Text>
                  {employee.performanceMetrics.achievements.map(
                    (achievement, index) => (
                      <div key={index} className='flex items-center gap-2 mb-1'>
                        <TrophyOutlined className='text-yellow-500 text-xs' />
                        <Text className='text-sm'>{achievement}</Text>
                      </div>
                    ),
                  )}
                </div>
              </Card>
            )}
          </Col>
        </Row>

        {/* Schedule */}
        {employee.schedule && (
          <Card
            title={
              <span className='flex items-center gap-2'>
                <ClockCircleOutlined className='text-blue-600' />
                {t('management.employees.schedule')}
              </span>
            }
            className='mb-6'
          >
            <Table
              dataSource={scheduleData}
              columns={scheduleColumns}
              pagination={false}
              size='small'
            />
          </Card>
        )}

        {/* Permissions */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <UserOutlined className='text-blue-600' />
              {t('management.employees.permissions')}
            </span>
          }
        >
          <Space wrap>
            {employee.permissions.map((permission, index) => (
              <Tag key={index} color='blue' className='mb-2'>
                {permission.replace('_', ' ').toUpperCase()}
              </Tag>
            ))}
          </Space>
        </Card>
      </div>
    </Modal>
  );
};

export default EmployeeDetailsModal;
