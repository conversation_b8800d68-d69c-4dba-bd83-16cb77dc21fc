import React, { useState } from 'react';
import {
  Modal,
  Table,
  Tag,
  Button,
  DatePicker,
  Select,
  Space,
  Card,
  Row,
  Col,
  Statistic,
  Calendar,
  Badge,
  Typography,
} from 'antd';
import {
  ClockCircleOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import type { Employee, AttendanceRecord } from '@/types';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title } = Typography;

interface AttendanceModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;
}

// Mock attendance data - in real app this would come from API
const generateMockAttendance = (employeeId: string): AttendanceRecord[] => {
  const records: AttendanceRecord[] = [];
  const today = dayjs();

  for (let i = 0; i < 30; i++) {
    const date = today.subtract(i, 'day');
    const isWeekend = date.day() === 0 || date.day() === 6;

    // Skip some weekend days
    if (isWeekend && Math.random() > 0.3) continue;

    const statuses: AttendanceRecord['status'][] = [
      'present',
      'late',
      'absent',
      'sick',
      'vacation',
    ];
    const weights = [0.7, 0.15, 0.05, 0.05, 0.05]; // Probability weights

    let status: AttendanceRecord['status'] = 'present';
    const rand = Math.random();
    let cumulative = 0;

    for (let j = 0; j < statuses.length; j++) {
      cumulative += weights[j];
      if (rand <= cumulative) {
        status = statuses[j];
        break;
      }
    }

    const clockIn =
      status === 'present' || status === 'late'
        ? date
            .hour(9)
            .minute(Math.random() > 0.8 ? 30 : 0)
            .format('YYYY-MM-DD HH:mm:ss')
        : undefined;

    const clockOut = clockIn
      ? date
          .hour(17)
          .minute(Math.random() > 0.8 ? 30 : 0)
          .format('YYYY-MM-DD HH:mm:ss')
      : undefined;

    const totalHours =
      clockIn && clockOut
        ? dayjs(clockOut).diff(dayjs(clockIn), 'hour', true)
        : undefined;

    records.push({
      id: `ATT_${employeeId}_${i}`,
      employeeId,
      date: date.format('YYYY-MM-DD'),
      clockIn,
      clockOut,
      totalHours,
      status,
      notes:
        status === 'sick'
          ? 'Medical leave'
          : status === 'vacation'
            ? 'Planned vacation'
            : status === 'late'
              ? 'Traffic delay'
              : undefined,
    });
  }

  return records.reverse();
};

const AttendanceModal: React.FC<AttendanceModalProps> = ({
  visible,
  employee,
  onClose,
}) => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('table');

  if (!employee) return null;

  const attendanceRecords = generateMockAttendance(employee.id);

  // Filter records based on date range and status
  const filteredRecords = attendanceRecords.filter((record) => {
    const recordDate = dayjs(record.date);
    const inDateRange =
      recordDate >= dateRange[0] && recordDate <= dateRange[1];
    const matchesStatus = !statusFilter || record.status === statusFilter;
    return inDateRange && matchesStatus;
  });

  // Calculate statistics
  const totalDays = filteredRecords.length;
  const presentDays = filteredRecords.filter(
    (r) => r.status === 'present',
  ).length;
  const lateDays = filteredRecords.filter((r) => r.status === 'late').length;
  const absentDays = filteredRecords.filter(
    (r) => r.status === 'absent',
  ).length;
  // const sickDays = filteredRecords.filter(r => r.status === 'sick').length;
  // const vacationDays = filteredRecords.filter(r => r.status === 'vacation').length;
  const attendanceRate =
    totalDays > 0 ? ((presentDays + lateDays) / totalDays) * 100 : 0;
  const avgHours =
    filteredRecords
      .filter((r) => r.totalHours)
      .reduce((sum, r) => sum + (r.totalHours || 0), 0) /
      filteredRecords.filter((r) => r.totalHours).length || 0;

  const getStatusColor = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return 'green';
      case 'late':
        return 'orange';
      case 'absent':
        return 'red';
      case 'sick':
        return 'purple';
      case 'vacation':
        return 'blue';
      case 'half_day':
        return 'yellow';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return <CheckCircleOutlined />;
      case 'late':
        return <WarningOutlined />;
      case 'absent':
        return <CloseCircleOutlined />;
      case 'sick':
      case 'vacation':
      case 'half_day':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const columns = [
    {
      title: t('management.employees.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a: AttendanceRecord, b: AttendanceRecord) =>
        dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: t('management.employees.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: AttendanceRecord['status']) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {t(`management.employees.attendanceStatus.${status}`)}
        </Tag>
      ),
    },
    {
      title: t('management.employees.clockIn'),
      dataIndex: 'clockIn',
      key: 'clockIn',
      render: (clockIn: string) =>
        clockIn ? dayjs(clockIn).format('HH:mm') : '-',
    },
    {
      title: t('management.employees.clockOut'),
      dataIndex: 'clockOut',
      key: 'clockOut',
      render: (clockOut: string) =>
        clockOut ? dayjs(clockOut).format('HH:mm') : '-',
    },
    {
      title: t('management.employees.totalHours'),
      dataIndex: 'totalHours',
      key: 'totalHours',
      render: (hours: number) => (hours ? `${hours.toFixed(1)}h` : '-'),
    },
    {
      title: t('management.employees.notes'),
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-',
    },
  ];

  const dateCellRender = (value: dayjs.Dayjs) => {
    const dateStr = value.format('YYYY-MM-DD');
    const record = attendanceRecords.find((r) => r.date === dateStr);

    if (!record) return null;

    return (
      <div className='text-center'>
        <Badge
          status={getStatusColor(record.status) as any}
          text={t(`management.employees.attendanceStatus.${record.status}`)}
        />
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <CalendarOutlined className='text-blue-600' />
          {t('management.employees.attendanceTracking')} - {employee.name}
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key='close' onClick={onClose}>
          {t('common.close')}
        </Button>,
      ]}
    >
      <div className='space-y-6'>
        {/* Controls */}
        <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
          <Space wrap>
            <RangePicker
              value={dateRange}
              onChange={(dates) =>
                dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])
              }
              format='MMM DD, YYYY'
            />
            <Select
              placeholder={t('management.employees.filterByStatus')}
              allowClear
              value={statusFilter || undefined}
              onChange={setStatusFilter}
              style={{ width: 150 }}
            >
              <Option value='present'>
                {t('management.employees.attendanceStatus.present')}
              </Option>
              <Option value='late'>
                {t('management.employees.attendanceStatus.late')}
              </Option>
              <Option value='absent'>
                {t('management.employees.attendanceStatus.absent')}
              </Option>
              <Option value='sick'>
                {t('management.employees.attendanceStatus.sick')}
              </Option>
              <Option value='vacation'>
                {t('management.employees.attendanceStatus.vacation')}
              </Option>
            </Select>
          </Space>

          <Space>
            <Button
              type={viewMode === 'table' ? 'primary' : 'default'}
              onClick={() => setViewMode('table')}
            >
              {t('management.employees.tableView')}
            </Button>
            <Button
              type={viewMode === 'calendar' ? 'primary' : 'default'}
              onClick={() => setViewMode('calendar')}
            >
              {t('management.employees.calendarView')}
            </Button>
          </Space>
        </div>

        {/* Statistics */}
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.attendanceRate')}
                value={attendanceRate}
                precision={1}
                suffix='%'
                valueStyle={{
                  color:
                    attendanceRate >= 95
                      ? '#52c41a'
                      : attendanceRate >= 85
                        ? '#faad14'
                        : '#ff4d4f',
                }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.presentDays')}
                value={presentDays}
                valueStyle={{ color: '#52c41a' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.lateDays')}
                value={lateDays}
                valueStyle={{ color: '#faad14' }}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.absentDays')}
                value={absentDays}
                valueStyle={{ color: '#ff4d4f' }}
                prefix={<CloseCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.avgHours')}
                value={avgHours}
                precision={1}
                suffix='h'
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card className='text-center'>
              <Statistic
                title={t('management.employees.totalDays')}
                value={totalDays}
                valueStyle={{ color: '#722ed1' }}
                prefix={<CalendarOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* Content */}
        {viewMode === 'table' ? (
          <Table
            dataSource={filteredRecords}
            columns={columns}
            rowKey='id'
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} records`,
            }}
          />
        ) : (
          <Calendar
            dateCellRender={dateCellRender}
            headerRender={({ value, onChange }) => (
              <div className='flex items-center justify-between p-4'>
                <Title level={4} className='mb-0'>
                  {value.format('MMMM YYYY')}
                </Title>
                <Space>
                  <Button onClick={() => onChange(value.subtract(1, 'month'))}>
                    Previous
                  </Button>
                  <Button onClick={() => onChange(dayjs())}>Today</Button>
                  <Button onClick={() => onChange(value.add(1, 'month'))}>
                    Next
                  </Button>
                </Space>
              </div>
            )}
          />
        )}
      </div>
    </Modal>
  );
};

export default AttendanceModal;
