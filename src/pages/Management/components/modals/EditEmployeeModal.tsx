import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  TimePicker,
  Checkbox,
  Divider,
  Button,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  DollarOutlined,
  HomeOutlined,
  ContactsOutlined,
  ClockCircleOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import type { Employee, UpdateEmployeeRequest } from '@/types';

const { Option } = Select;
const { TextArea } = Input;

interface EditEmployeeModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;

  onSave: (employeeId: string, data: UpdateEmployeeRequest) => void;
  loading?: boolean;
}

const EditEmployeeModal: React.FC<EditEmployeeModalProps> = ({
  visible,
  employee,
  onClose,
  onSave,
  loading = false,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (employee && visible) {
      // Set form values when employee data is available
      form.setFieldsValue({
        name: employee.name,
        email: employee.email,
        phone: employee.phone,
        role: employee.role,
        department: employee.department,
        salary: employee.salary,
        address: employee.address,
        emergencyContactName: employee.emergencyContact?.name,
        emergencyContactPhone: employee.emergencyContact?.phone,
        emergencyContactRelationship: employee.emergencyContact?.relationship,
        permissions: employee.permissions,
        // Schedule
        mondayOff: employee.schedule?.monday.off,
        mondayStart: employee.schedule?.monday.start
          ? dayjs(employee.schedule.monday.start, 'HH:mm')
          : null,
        mondayEnd: employee.schedule?.monday.end
          ? dayjs(employee.schedule.monday.end, 'HH:mm')
          : null,
        tuesdayOff: employee.schedule?.tuesday.off,
        tuesdayStart: employee.schedule?.tuesday.start
          ? dayjs(employee.schedule.tuesday.start, 'HH:mm')
          : null,
        tuesdayEnd: employee.schedule?.tuesday.end
          ? dayjs(employee.schedule.tuesday.end, 'HH:mm')
          : null,
        wednesdayOff: employee.schedule?.wednesday.off,
        wednesdayStart: employee.schedule?.wednesday.start
          ? dayjs(employee.schedule.wednesday.start, 'HH:mm')
          : null,
        wednesdayEnd: employee.schedule?.wednesday.end
          ? dayjs(employee.schedule.wednesday.end, 'HH:mm')
          : null,
        thursdayOff: employee.schedule?.thursday.off,
        thursdayStart: employee.schedule?.thursday.start
          ? dayjs(employee.schedule.thursday.start, 'HH:mm')
          : null,
        thursdayEnd: employee.schedule?.thursday.end
          ? dayjs(employee.schedule.thursday.end, 'HH:mm')
          : null,
        fridayOff: employee.schedule?.friday.off,
        fridayStart: employee.schedule?.friday.start
          ? dayjs(employee.schedule.friday.start, 'HH:mm')
          : null,
        fridayEnd: employee.schedule?.friday.end
          ? dayjs(employee.schedule.friday.end, 'HH:mm')
          : null,
        saturdayOff: employee.schedule?.saturday.off,
        saturdayStart: employee.schedule?.saturday.start
          ? dayjs(employee.schedule.saturday.start, 'HH:mm')
          : null,
        saturdayEnd: employee.schedule?.saturday.end
          ? dayjs(employee.schedule.saturday.end, 'HH:mm')
          : null,
        sundayOff: employee.schedule?.sunday.off,
        sundayStart: employee.schedule?.sunday.start
          ? dayjs(employee.schedule.sunday.start, 'HH:mm')
          : null,
        sundayEnd: employee.schedule?.sunday.end
          ? dayjs(employee.schedule.sunday.end, 'HH:mm')
          : null,
      });
    }
  }, [employee, visible, form]);

  const handleSubmit = async (values: any) => {
    if (!employee) return;

    const updateData: UpdateEmployeeRequest = {
      name: values.name,
      email: values.email,
      phone: values.phone,
      role: values.role,
      department: values.department,
      salary: values.salary,
      address: values.address,
      permissions: values.permissions,
      emergencyContact: {
        name: values.emergencyContactName,
        phone: values.emergencyContactPhone,
        relationship: values.emergencyContactRelationship,
      },
      schedule: {
        monday: {
          off: values.mondayOff,
          start: values.mondayOff
            ? ''
            : values.mondayStart?.format('HH:mm') || '',
          end: values.mondayOff ? '' : values.mondayEnd?.format('HH:mm') || '',
        },
        tuesday: {
          off: values.tuesdayOff,
          start: values.tuesdayOff
            ? ''
            : values.tuesdayStart?.format('HH:mm') || '',
          end: values.tuesdayOff
            ? ''
            : values.tuesdayEnd?.format('HH:mm') || '',
        },
        wednesday: {
          off: values.wednesdayOff,
          start: values.wednesdayOff
            ? ''
            : values.wednesdayStart?.format('HH:mm') || '',
          end: values.wednesdayOff
            ? ''
            : values.wednesdayEnd?.format('HH:mm') || '',
        },
        thursday: {
          off: values.thursdayOff,
          start: values.thursdayOff
            ? ''
            : values.thursdayStart?.format('HH:mm') || '',
          end: values.thursdayOff
            ? ''
            : values.thursdayEnd?.format('HH:mm') || '',
        },
        friday: {
          off: values.fridayOff,
          start: values.fridayOff
            ? ''
            : values.fridayStart?.format('HH:mm') || '',
          end: values.fridayOff ? '' : values.fridayEnd?.format('HH:mm') || '',
        },
        saturday: {
          off: values.saturdayOff,
          start: values.saturdayOff
            ? ''
            : values.saturdayStart?.format('HH:mm') || '',
          end: values.saturdayOff
            ? ''
            : values.saturdayEnd?.format('HH:mm') || '',
        },
        sunday: {
          off: values.sundayOff,
          start: values.sundayOff
            ? ''
            : values.sundayStart?.format('HH:mm') || '',
          end: values.sundayOff ? '' : values.sundayEnd?.format('HH:mm') || '',
        },
      },
    };

    onSave(employee.id, updateData);
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  const availablePermissions = [
    'all',
    'manage_employees',
    'view_reports',
    'manage_shifts',
    'operate_register',
    'process_transactions',
    'monitor_premises',
    'incident_reports',
    'equipment_maintenance',
    'facility_repairs',
  ];

  const renderScheduleRow = (day: string, dayKey: string) => (
    <Row gutter={16} key={day} className='mb-4'>
      <Col span={4}>
        <div className='font-medium text-gray-700 pt-2'>
          {t(`management.employees.days.${day.toLowerCase()}`)}
        </div>
      </Col>
      <Col span={4}>
        <Form.Item
          name={`${dayKey}Off`}
          valuePropName='checked'
          className='mb-0'
        >
          <Checkbox>{t('management.employees.dayOff')}</Checkbox>
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          name={`${dayKey}Start`}
          className='mb-0'
          dependencies={[`${dayKey}Off`]}
        >
          <TimePicker
            format='HH:mm'
            placeholder={t('management.employees.startTime')}
            disabled={form.getFieldValue(`${dayKey}Off`)}
            className='w-full'
          />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          name={`${dayKey}End`}
          className='mb-0'
          dependencies={[`${dayKey}Off`]}
        >
          <TimePicker
            format='HH:mm'
            placeholder={t('management.employees.endTime')}
            disabled={form.getFieldValue(`${dayKey}Off`)}
            className='w-full'
          />
        </Form.Item>
      </Col>
    </Row>
  );

  // @ts-ignore
  // @ts-ignore
  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <UserOutlined className='text-blue-600' />
          {t('management.employees.editEmployee')}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key='cancel' onClick={handleCancel}>
          {t('common.cancel')}
        </Button>,
        <Button
          key='save'
          type='primary'
          loading={loading}
          onClick={() => form.submit()}
          icon={<SaveOutlined />}
          className='bg-blue-600 hover:bg-blue-700'
        >
          {t('common.save')}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        className='max-h-[70vh] overflow-y-auto'
      >
        {/* Basic Information */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <UserOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('management.employees.basicInfo')}
            </span>
          </div>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='name'
                label={t('management.employees.name')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.nameRequired'),
                  },
                ]}
              >
                <Input prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='email'
                label={t('management.employees.email')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.emailRequired'),
                  },
                  {
                    type: 'email',
                    message: t('management.employees.emailInvalid'),
                  },
                ]}
              >
                <Input prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='phone'
                label={t('management.employees.phone')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.phoneRequired'),
                  },
                ]}
              >
                <Input prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='salary'
                label={t('management.employees.salary')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.salaryRequired'),
                  },
                ]}
              >
                <InputNumber
                  prefix={<DollarOutlined />}
                  className='w-full'
                  min={0}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  //@ts-ignore
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='role'
                label={t('management.employees.role')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.roleRequired'),
                  },
                ]}
              >
                <Select>
                  <Option value='admin'>
                    {t('management.employees.roles.admin')}
                  </Option>
                  <Option value='manager'>
                    {t('management.employees.roles.manager')}
                  </Option>
                  <Option value='cashier'>
                    {t('management.employees.roles.cashier')}
                  </Option>
                  <Option value='security'>
                    {t('management.employees.roles.security')}
                  </Option>
                  <Option value='maintenance'>
                    {t('management.employees.roles.maintenance')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='department'
                label={t('management.employees.department')}
                rules={[
                  {
                    required: true,
                    message: t('management.employees.departmentRequired'),
                  },
                ]}
              >
                <Select>
                  <Option value='management'>
                    {t('management.employees.departments.management')}
                  </Option>
                  <Option value='gaming'>
                    {t('management.employees.departments.gaming')}
                  </Option>
                  <Option value='security'>
                    {t('management.employees.departments.security')}
                  </Option>
                  <Option value='maintenance'>
                    {t('management.employees.departments.maintenance')}
                  </Option>
                  <Option value='customer_service'>
                    {t('management.employees.departments.customer_service')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name='address' label={t('management.employees.address')}>
            <TextArea
              rows={2}
              //@ts-ignore
              prefix={<HomeOutlined />}
            />
          </Form.Item>
        </div>

        <Divider />

        {/* Emergency Contact */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <ContactsOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('management.employees.emergencyContact')}
            </span>
          </div>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name='emergencyContactName'
                label={t('management.employees.contactName')}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='emergencyContactPhone'
                label={t('management.employees.contactPhone')}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='emergencyContactRelationship'
                label={t('management.employees.relationship')}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* Permissions */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <UserOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('management.employees.permissions')}
            </span>
          </div>

          <Form.Item
            name='permissions'
            rules={[
              {
                required: true,
                message: t('management.employees.permissionsRequired'),
              },
            ]}
          >
            <Select
              mode='multiple'
              placeholder={t('management.employees.selectPermissions')}
              className='w-full'
            >
              {availablePermissions.map((permission) => (
                <Option key={permission} value={permission}>
                  {permission.replace('_', ' ').toUpperCase()}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Divider />

        {/* Schedule */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <ClockCircleOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('management.employees.schedule')}
            </span>
          </div>

          <div className='bg-gray-50 p-4 rounded-lg'>
            <Row gutter={16} className='mb-2'>
              <Col span={4}>
                <strong>{t('management.employees.day')}</strong>
              </Col>
              <Col span={4}>
                <strong>{t('management.employees.dayOff')}</strong>
              </Col>
              <Col span={8}>
                <strong>{t('management.employees.startTime')}</strong>
              </Col>
              <Col span={8}>
                <strong>{t('management.employees.endTime')}</strong>
              </Col>
            </Row>

            {renderScheduleRow('Monday', 'monday')}
            {renderScheduleRow('Tuesday', 'tuesday')}
            {renderScheduleRow('Wednesday', 'wednesday')}
            {renderScheduleRow('Thursday', 'thursday')}
            {renderScheduleRow('Friday', 'friday')}
            {renderScheduleRow('Saturday', 'saturday')}
            {renderScheduleRow('Sunday', 'sunday')}
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default EditEmployeeModal;
