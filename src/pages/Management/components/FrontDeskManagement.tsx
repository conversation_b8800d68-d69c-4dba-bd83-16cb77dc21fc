import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  DatePicker,
  InputNumber,
} from 'antd';
import {
  BankOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SolutionOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import {
  useCashRegisters,
  useTransactions,
  useShifts,
  useOpenCashRegister,
  useCloseCashRegister,
  useCreateTransaction,
  useEndShift,
} from '@/hooks/useManagement.ts';
import { useEmployees } from '@/hooks/useManagement.ts';
import { formatCurrency, formatDateTime } from '@/utils/tableUtils.ts';
import GalaxyTable from '../../../components/GalaxyTable';
import type { CashRegister, Transaction, Shift } from '@/types';

const { Option } = Select;
const { Text } = Typography;
const { RangePicker } = DatePicker;

const FrontDeskManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const [activeTab, setActiveTab] = useState('registers');
  const [showOpenModal, setShowOpenModal] = useState(false);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [selectedRegister, setSelectedRegister] = useState<CashRegister | null>(
    null,
  );

  // Responsive values
  const selectWidth = useResponsiveValue({
    xs: '100%',
    sm: '160px',
    md: '160px',
    lg: '160px',
    xl: '160px',
    '2xl': '160px'
  });

  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: 500,
    lg: 500,
    xl: 500,
    '2xl': 500
  });
  // @ts-ignore
  // ESLint-disable-next-line @typescript-eslint/no-unused-vars
  // const [selectedShift, setSelectedShift] = useState<Shift | null>(null);

  // Filters
  const [registerStatusFilter, setRegisterStatusFilter] = useState<string>('');
  const [transactionTypeFilter, setTransactionTypeFilter] =
    useState<string>('');
  const [dateRange, setDateRange] = useState<[any, any] | null>(null);

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  const [openForm] = Form.useForm();
  const [closeForm] = Form.useForm();
  const [transactionForm] = Form.useForm();

  // Mutations
  const openRegisterMutation = useOpenCashRegister();
  const closeRegisterMutation = useCloseCashRegister();
  const createTransactionMutation = useCreateTransaction();
  const endShiftMutation = useEndShift();

  // Queries
  const { data: registersResponse, isLoading: registersLoading } =
    useCashRegisters({
      page: pagination.current,
      pageSize: pagination.pageSize,
      status: registerStatusFilter || undefined,
    });

  const { data: transactionsResponse, isLoading: transactionsLoading } =
    useTransactions({
      page: pagination.current,
      pageSize: pagination.pageSize,
      type: transactionTypeFilter || undefined,
      dateFrom: dateRange?.[0]?.format('YYYY-MM-DD'),
      dateTo: dateRange?.[1]?.format('YYYY-MM-DD'),
    });

  const { data: shiftsResponse, isLoading: shiftsLoading } = useShifts({
    page: pagination.current,
    pageSize: pagination.pageSize,
  });

  const { data: employeesResponse } = useEmployees({});

  const registers = registersResponse?.data?.data || [];
  const transactions = transactionsResponse?.data?.data || [];
  const shifts = shiftsResponse?.data?.data || [];
  const employees = employeesResponse?.data?.data || [];
  const total =
    registersResponse?.data?.total ||
    transactionsResponse?.data?.total ||
    shiftsResponse?.data?.total ||
    0;

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleOpenRegister = async (values: any) => {
    try {
      await openRegisterMutation.mutateAsync({
        registerId: selectedRegister!.id,
        employeeId: values.employeeId,
        openingBalance: values.openingBalance,
      });
      setShowOpenModal(false);
      openForm.resetFields();
      setSelectedRegister(null);
    } catch (error) {
      console.error('Failed to open register:', error);
    }
  };

  const handleCloseRegister = async (values: any) => {
    try {
      await closeRegisterMutation.mutateAsync({
        registerId: selectedRegister!.id,
        closingBalance: values.closingBalance,
      });
      setShowCloseModal(false);
      closeForm.resetFields();
      setSelectedRegister(null);
    } catch (error) {
      console.error('Failed to close register:', error);
    }
  };

  const handleCreateTransaction = async (values: any) => {
    try {
      await createTransactionMutation.mutateAsync({
        registerId: values.registerId,
        type: values.type,
        amount: values.amount,
        description: values.description,
        employeeId: values.employeeId,
        employeeName:
          employees.find((emp) => emp.id === values.employeeId)?.name || '',
      });
      setShowTransactionModal(false);
      transactionForm.resetFields();
    } catch (error) {
      console.error('Failed to create transaction:', error);
    }
  };

  const handleEndShift = async (shift: Shift) => {
    Modal.confirm({
      title: 'End Shift',
      content: 'Are you sure you want to end this shift?',
      onOk: async () => {
        try {
          const register = registers.find((r) => r.id === shift.registerId);
          await endShiftMutation.mutateAsync({
            id: shift.id,
            closingBalance: register?.currentBalance || 0,
          });
        } catch (error) {
          console.error('Failed to end shift:', error);
        }
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
      case 'active':
        return 'green';
      case 'closed':
      case 'completed':
        return 'blue';
      case 'maintenance':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'sale':
        return 'green';
      case 'refund':
        return 'red';
      case 'void':
        return 'orange';
      case 'cash_in':
        return 'blue';
      case 'cash_out':
        return 'purple';
      default:
        return 'default';
    }
  };

  // Register columns
  const registerColumns = [
    {
      title: t('management.frontDesk.registerName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: CashRegister) => (
        <div>
          <div className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}>{name}</div>
          {!isMobile && (
            <div className='text-sm text-gray-500'>{record.location}</div>
          )}
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('management.frontDesk.employee'),
      dataIndex: 'employeeName',
      key: 'employeeName',
      render: (name: string) => (
        <span className={isMobile ? 'text-sm' : ''}>{name || '-'}</span>
      ),
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: CashRegister['status']) => (
        <Tag color={getStatusColor(status)} className={`font-medium ${isMobile ? 'text-xs' : ''}`}>
          {isMobile
            ? status.charAt(0).toUpperCase() + status.slice(1)
            : t(`management.frontDesk.registerStatuses.${status}`)
          }
        </Tag>
      ),
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('management.frontDesk.currentBalance'),
      dataIndex: 'currentBalance',
      key: 'currentBalance',
      render: (balance: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(balance)}
        </span>
      ),
    },
    {
      title: t('management.frontDesk.openedAt'),
      dataIndex: 'openedAt',
      key: 'openedAt',
      render: (date: string) => (date ? formatDateTime(date) : '-'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: CashRegister) => (
        <Space>
          {record.status === 'closed' && (
            <Button
              type='primary'
              size='small'
              icon={<PlayCircleOutlined />}
              onClick={() => {
                setSelectedRegister(record);
                setShowOpenModal(true);
              }}
              className='bg-green-600 hover:bg-green-700'
            >
              {t('management.frontDesk.openRegister')}
            </Button>
          )}
          {record.status === 'open' && (
            <Button
              type='primary'
              size='small'
              icon={<StopOutlined />}
              onClick={() => {
                setSelectedRegister(record);
                setShowCloseModal(true);
              }}
              className='bg-red-600 hover:bg-red-700'
            >
              {t('management.frontDesk.closeRegister')}
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // Transaction columns
  const transactionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: t('management.frontDesk.transactionType'),
      dataIndex: 'type',
      key: 'type',
      render: (type: Transaction['type']) => (
        <Tag color={getTransactionTypeColor(type)} className='font-medium'>
          {t(`management.frontDesk.transactionTypes.${type}`)}
        </Tag>
      ),
    },
    {
      title: t('management.frontDesk.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span
          className={`font-medium ${amount >= 0 ? 'text-green-600' : 'text-red-600'}`}
        >
          {formatCurrency(amount)}
        </span>
      ),
    },
    {
      title: t('management.frontDesk.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('management.frontDesk.employee'),
      dataIndex: 'employeeName',
      key: 'employeeName',
    },
    {
      title: t('management.frontDesk.timestamp'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (date: string) => formatDateTime(date),
    },
    {
      title: t('management.frontDesk.receiptNumber'),
      dataIndex: 'receiptNumber',
      key: 'receiptNumber',
      render: (receipt: string) => receipt || '-',
    },
  ];

  // Shift columns
  const shiftColumns = [
    {
      title: 'Shift ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: 'Register',
      dataIndex: 'registerId',
      key: 'registerId',
      render: (registerId: string) => {
        const register = registers.find((r) => r.id === registerId);
        return register?.name || registerId;
      },
    },
    {
      title: t('management.frontDesk.employee'),
      dataIndex: 'employeeName',
      key: 'employeeName',
    },
    {
      title: t('management.frontDesk.shiftStart'),
      dataIndex: 'startTime',
      key: 'startTime',
      render: (date: string) => formatDateTime(date),
    },
    {
      title: t('management.frontDesk.shiftEnd'),
      dataIndex: 'endTime',
      key: 'endTime',
      render: (date: string) => (date ? formatDateTime(date) : '-'),
    },
    {
      title: t('management.frontDesk.totalSales'),
      dataIndex: 'totalSales',
      key: 'totalSales',
      render: (amount: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(amount)}
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: Shift['status']) => (
        <Tag color={getStatusColor(status)} className='font-medium'>
          {status === 'active' ? 'Active' : 'Completed'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Shift) => (
        <Space>
          {record.status === 'active' && (
            <Button
              type='primary'
              size='small'
              icon={<StopOutlined />}
              onClick={() => handleEndShift(record)}
              className='bg-red-600 hover:bg-red-700'
            >
              {t('management.frontDesk.endShift')}
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const tabItems = [
    {
      key: 'registers',
      label: (
        <span className='flex items-center gap-2'>
          <BankOutlined />
          {t('management.frontDesk.registers')}
        </span>
      ),
      children: (
        <div className={`space-y-4 ${isMobile ? 'sm:space-y-3' : ''}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-between items-center'}`}>
            <div className={`flex ${isMobile ? 'flex-col gap-2' : 'gap-4'}`}>
              <Select
                placeholder='Filter by status'
                allowClear
                value={registerStatusFilter || undefined}
                onChange={setRegisterStatusFilter}
                style={{ width: selectWidth }}
                size={isMobile ? 'middle' : 'default'}
              >
                <Option value='open'>
                  {t('management.frontDesk.registerStatuses.open')}
                </Option>
                <Option value='closed'>
                  {t('management.frontDesk.registerStatuses.closed')}
                </Option>
                <Option value='maintenance'>
                  {t('management.frontDesk.registerStatuses.maintenance')}
                </Option>
              </Select>
            </div>
          </div>

          <GalaxyTable
            data={registers}
            columns={registerColumns}
            loading={registersLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: handlePaginationChange,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: !isMobile ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined,
            }}
            rowKey='id'
            scroll={isMobile ? { x: 600 } : undefined}
          />
        </div>
      ),
    },
    {
      key: 'transactions',
      label: (
        <span className='flex items-center gap-2'>
          <SolutionOutlined />
          {t('management.frontDesk.transactions')}
        </span>
      ),
      children: (
        <div className={`space-y-4 ${isMobile ? 'sm:space-y-3' : ''}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-between items-center'}`}>
            <div className={`flex ${isMobile ? 'flex-col gap-2' : 'gap-4'}`}>
              <Select
                placeholder='Filter by type'
                allowClear
                value={transactionTypeFilter || undefined}
                onChange={setTransactionTypeFilter}
                style={{ width: selectWidth }}
                size={isMobile ? 'middle' : 'default'}
              >
                <Option value='sale'>
                  {t('management.frontDesk.transactionTypes.sale')}
                </Option>
                <Option value='refund'>
                  {t('management.frontDesk.transactionTypes.refund')}
                </Option>
                <Option value='void'>
                  {t('management.frontDesk.transactionTypes.void')}
                </Option>
                <Option value='cash_in'>
                  {t('management.frontDesk.transactionTypes.cash_in')}
                </Option>
                <Option value='cash_out'>
                  {t('management.frontDesk.transactionTypes.cash_out')}
                </Option>
              </Select>

              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={[isMobile ? 'Start' : 'Start Date', isMobile ? 'End' : 'End Date']}
                size={isMobile ? 'middle' : 'default'}
                className={isMobile ? 'w-full' : ''}
              />
            </div>

            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => setShowTransactionModal(true)}
              className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
              size={isMobile ? 'middle' : 'default'}
            >
              {isMobile ? 'Add' : t('management.frontDesk.addTransaction')}
            </Button>
          </div>

          <GalaxyTable
            data={transactions}
            columns={transactionColumns}
            loading={transactionsLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: handlePaginationChange,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: !isMobile ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined,
            }}
            rowKey='id'
            scroll={isMobile ? { x: 800 } : undefined}
          />
        </div>
      ),
    },
    {
      key: 'shifts',
      label: (
        <span className='flex items-center gap-2'>
          <ClockCircleOutlined />
          {t('management.frontDesk.shifts')}
        </span>
      ),
      children: (
        <div className={`space-y-4 ${isMobile ? 'sm:space-y-3' : ''}`}>
          <GalaxyTable
            data={shifts}
            columns={shiftColumns}
            loading={shiftsLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: handlePaginationChange,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: !isMobile ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined,
            }}
            rowKey='id'
            scroll={isMobile ? { x: 700 } : undefined}
          />
        </div>
      ),
    },
  ];

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6'}`}>
      <Card className={`${isMobile ? 'rounded-xl' : 'rounded-xl'} border-0 shadow-sm`}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size={isMobile ? 'default' : 'large'}
          tabPosition={isMobile ? 'top' : 'top'}
        />
      </Card>

      {/* Open Register Modal */}
      <Modal
        title={
          <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
            <PlayCircleOutlined className='text-green-600' />
            <span className={isMobile ? 'text-sm' : ''}>{t('management.frontDesk.openRegister')}</span>
          </div>
        }
        open={showOpenModal}
        onCancel={() => {
          setShowOpenModal(false);
          openForm.resetFields();
          setSelectedRegister(null);
        }}
        footer={null}
        width={modalWidth}
        className={isMobile ? 'top-4' : 'top-8'}
        styles={{
          body: { padding: isMobile ? '16px' : '24px' }
        }}
      >
        <Form
          form={openForm}
          layout='vertical'
          onFinish={handleOpenRegister}
          className='mt-6'
        >
          <Form.Item
            name='employeeId'
            label={t('management.frontDesk.employee')}
            rules={[{ required: true, message: 'Employee is required' }]}
          >
            <Select placeholder='Select employee' size='large'>
              {employees.map((employee) => (
                <Option key={employee.id} value={employee.id}>
                  {employee.name} - {employee.role}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name='openingBalance'
            label={t('management.frontDesk.openingBalance')}
            rules={[{ required: true, message: 'Opening balance is required' }]}
          >
            <InputNumber
              prefix='$'
              placeholder='Enter opening balance'
              size='large'
              className='w-full'
              min={0}
              step={0.01}
            />
          </Form.Item>

          <div className='flex justify-end gap-3 mt-6'>
            <Button
              onClick={() => {
                setShowOpenModal(false);
                openForm.resetFields();
                setSelectedRegister(null);
              }}
              size='large'
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={openRegisterMutation.isPending}
              size='large'
              className='bg-green-600 hover:bg-green-700'
            >
              {t('management.frontDesk.openRegister')}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Close Register Modal */}
      <Modal
        title={
          <div className='flex items-center gap-2'>
            <StopOutlined className='text-red-600' />
            {t('management.frontDesk.closeRegister')}
          </div>
        }
        open={showCloseModal}
        onCancel={() => {
          setShowCloseModal(false);
          closeForm.resetFields();
          setSelectedRegister(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={closeForm}
          layout='vertical'
          onFinish={handleCloseRegister}
          className='mt-6'
        >
          <div className='mb-4 p-4 bg-gray-50 rounded-lg'>
            <Text className='text-sm text-gray-600'>Current Balance: </Text>
            <Text className='font-medium text-lg'>
              {formatCurrency(selectedRegister?.currentBalance || 0)}
            </Text>
          </div>

          <Form.Item
            name='closingBalance'
            label={t('management.frontDesk.closingBalance')}
            rules={[{ required: true, message: 'Closing balance is required' }]}
          >
            <InputNumber
              prefix='$'
              placeholder='Enter actual closing balance'
              size='large'
              className='w-full'
              min={0}
              step={0.01}
            />
          </Form.Item>

          <div className='flex justify-end gap-3 mt-6'>
            <Button
              onClick={() => {
                setShowCloseModal(false);
                closeForm.resetFields();
                setSelectedRegister(null);
              }}
              size='large'
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={closeRegisterMutation.isPending}
              size='large'
              className='bg-red-600 hover:bg-red-700'
            >
              {t('management.frontDesk.closeRegister')}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Add Transaction Modal */}
      <Modal
        title={
          <div className='flex items-center gap-2'>
            <SolutionOutlined className='text-blue-600' />
            {t('management.frontDesk.addTransaction')}
          </div>
        }
        open={showTransactionModal}
        onCancel={() => {
          setShowTransactionModal(false);
          transactionForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={transactionForm}
          layout='vertical'
          onFinish={handleCreateTransaction}
          className='mt-6'
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='registerId'
                label='Register'
                rules={[{ required: true, message: 'Register is required' }]}
              >
                <Select placeholder='Select register' size='large'>
                  {registers
                    .filter((r) => r.status === 'open')
                    .map((register) => (
                      <Option key={register.id} value={register.id}>
                        {register.name}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='employeeId'
                label={t('management.frontDesk.employee')}
                rules={[{ required: true, message: 'Employee is required' }]}
              >
                <Select placeholder='Select employee' size='large'>
                  {employees.map((employee) => (
                    <Option key={employee.id} value={employee.id}>
                      {employee.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='type'
                label={t('management.frontDesk.transactionType')}
                rules={[{ required: true, message: 'Type is required' }]}
              >
                <Select placeholder='Select type' size='large'>
                  <Option value='sale'>
                    {t('management.frontDesk.transactionTypes.sale')}
                  </Option>
                  <Option value='refund'>
                    {t('management.frontDesk.transactionTypes.refund')}
                  </Option>
                  <Option value='void'>
                    {t('management.frontDesk.transactionTypes.void')}
                  </Option>
                  <Option value='cash_in'>
                    {t('management.frontDesk.transactionTypes.cash_in')}
                  </Option>
                  <Option value='cash_out'>
                    {t('management.frontDesk.transactionTypes.cash_out')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='amount'
                label={t('management.frontDesk.amount')}
                rules={[{ required: true, message: 'Amount is required' }]}
              >
                <InputNumber
                  prefix='$'
                  placeholder='Enter amount'
                  size='large'
                  className='w-full'
                  step={0.01}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name='description'
            label={t('management.frontDesk.description')}
            rules={[{ required: true, message: 'Description is required' }]}
          >
            <Input.TextArea
              placeholder='Enter transaction description'
              rows={3}
              size='large'
            />
          </Form.Item>

          <div className='flex justify-end gap-3 mt-6'>
            <Button
              onClick={() => {
                setShowTransactionModal(false);
                transactionForm.resetFields();
              }}
              size='large'
            >
              {t('common.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={createTransactionMutation.isPending}
              size='large'
              className='bg-blue-600 hover:bg-blue-700'
            >
              {t('common.save')}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default FrontDeskManagement;
