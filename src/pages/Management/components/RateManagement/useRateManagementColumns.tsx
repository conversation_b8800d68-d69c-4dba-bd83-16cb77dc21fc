import {
  DollarOutlined,
  MoreOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Button,
  Dropdown,
  Switch,
  Typography,
  type TableColumnType,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { type ExchangeRate } from '@/types';
import { dateFormator } from '@/utils/dateUtils';

const { Text } = Typography;

interface UseProps {
  setOpenEditPriceFrom: React.Dispatch<
    React.SetStateAction<ExchangeRate | undefined>
  >;
  setOpenSettingsFrom: React.Dispatch<
    React.SetStateAction<ExchangeRate | undefined>
  >;
}

export default function useRateManagementColumns(useProps: UseProps) {
  const { setOpenEditPriceFrom, setOpenSettingsFrom } = useProps || {};

  const { t } = useTranslation();

  const columns: Array<TableColumnType<ExchangeRate>> = [
    {
      title: t('management.exchangeRates.fiatCode'),
      dataIndex: 'fiatCode',
      key: 'fiatCode',
      align: 'center',
      render: (_, { fiatCode }) => <Text>{fiatCode}</Text>,
    },
    {
      title: t('management.exchangeRates.bidPrice'),
      dataIndex: 'bidPrice',
      key: 'bidPrice',
      align: 'right',
      sorter: true,
      render: (_, { bidPrice }) => (
        <Text className='!text-green-500'>{bidPrice}</Text>
      ),
    },
    {
      title: t('management.exchangeRates.spotPrice'),
      dataIndex: 'spotPrice',
      key: 'spotPrice',
      align: 'right',
      sorter: true,
      render: (_, { spotPrice }) => (
        <Text className='text-primary'>{spotPrice}</Text>
      ),
    },
    {
      title: t('management.exchangeRates.askPrice'),
      dataIndex: 'askPrice',
      key: 'askPrice',
      align: 'right',
      sorter: true,
      render: (_, { askPrice }) => (
        <Text className='!text-red-500'>{askPrice}</Text>
      ),
    },
    {
      title: t('management.exchangeRates.updatedAt'),
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center',
      render: (_, { updatedAt }) => (
        <Text>{dayjs(updatedAt).format(dateFormator.accurate)}</Text>
      ),
    },
    {
      title: t('management.exchangeRates.autoUpdate'),
      dataIndex: 'autoUpdate',
      key: 'autoUpdate',
      align: 'center',
      render: (_, { autoUpdate }) => (
        <Switch
          disabled
          checked={autoUpdate}
          unCheckedChildren={t('common.off')}
          checkedChildren={t('common.on')}
        />
      ),
    },
    {
      title: t('management.exchangeRates.updateNote'),
      dataIndex: 'updateNote',
      key: 'updateNote',
      render: (_, { updateNote }) => <Text>{updateNote || '--'}</Text>,
    },
    {
      title: t('management.exchangeRates.actions'),
      key: 'actions',
      align: 'center',
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'edit-price',
                label: t('management.exchangeRates.editPrice'),
                icon: <DollarOutlined />,
                onClick: () => {
                  setOpenEditPriceFrom(record);
                },
              },
              {
                key: 'settings',
                label: t('management.exchangeRates.settings'),
                icon: <SettingOutlined />,
                onClick: () => {
                  setOpenSettingsFrom(record);
                },
              },
            ],
          }}
          trigger={['click']}
          placement='bottomRight'
        >
          <Button icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return { columns };
}
