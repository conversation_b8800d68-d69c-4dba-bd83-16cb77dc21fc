import {
  mockEmployees,
  mockCashRegisters,
  mockTransactions,
  mockShifts,
  mockStores,
  mockProducts,
  mockManagementStats,
  mockDelay,
  mockExchangeRates,
} from '@/api/mockData.ts';
import type {
  Employee,
  CreateEmployeeRequest,
  CashRegister,
  Transaction,
  Shift,
  Store,
  CreateStoreRequest,
  Product,
  ManagementStats,
  ApiResponse,
  PaginatedResponse,
  ExchangeRate,
} from '@/types';

// Management Stats API
export const fetchManagementStats = async (): Promise<
  ApiResponse<ManagementStats>
> => {
  await mockDelay();

  return {
    data: mockManagementStats,
    success: true,
    message: 'Management stats fetched successfully',
  };
};

// Employee APIs
export const fetchEmployees = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: string;
  department?: string;
  status?: string;
}): Promise<ApiResponse<PaginatedResponse<Employee>>> => {
  await mockDelay();

  const {
    page = 1,
    pageSize = 10,
    search = '',
    role,
    department,
    status,
  } = params || {};

  // Filter employees based on parameters
  let filteredEmployees = mockEmployees;

  if (search) {
    filteredEmployees = filteredEmployees.filter(
      (employee) =>
        employee.name.toLowerCase().includes(search.toLowerCase()) ||
        employee.email.toLowerCase().includes(search.toLowerCase()),
    );
  }

  if (role) {
    filteredEmployees = filteredEmployees.filter(
      (employee) => employee.role === role,
    );
  }

  if (department) {
    filteredEmployees = filteredEmployees.filter(
      (employee) => employee.department === department,
    );
  }

  if (status) {
    filteredEmployees = filteredEmployees.filter(
      (employee) => employee.status === status,
    );
  }

  // Pagination
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedEmployees = filteredEmployees.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedEmployees,
      total: filteredEmployees.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Employees fetched successfully',
  };
};

export const fetchEmployeeById = async (
  id: string,
): Promise<ApiResponse<Employee>> => {
  await mockDelay();

  const employee = mockEmployees.find((emp) => emp.id === id);

  if (!employee) {
    throw new Error('Employee not found');
  }

  return {
    data: employee,
    success: true,
    message: 'Employee fetched successfully',
  };
};

export const createEmployee = async (
  employeeData: CreateEmployeeRequest,
): Promise<ApiResponse<Employee>> => {
  await mockDelay();

  const newEmployee: Employee = {
    id: `EMP${Date.now()}`,
    ...employeeData,
    status: 'active',
    hireDate: new Date().toISOString(),
    lastLogin: undefined,
    performanceScore: 0,
    attendanceRate: 100,
  };

  mockEmployees.unshift(newEmployee);

  return {
    data: newEmployee,
    success: true,
    message: 'Employee created successfully',
  };
};

export const updateEmployeeStatus = async (
  id: string,
  status: Employee['status'],
): Promise<ApiResponse<Employee>> => {
  await mockDelay();

  const employeeIndex = mockEmployees.findIndex((emp) => emp.id === id);

  if (employeeIndex === -1) {
    throw new Error('Employee not found');
  }

  mockEmployees[employeeIndex].status = status;

  return {
    data: mockEmployees[employeeIndex],
    success: true,
    message: 'Employee status updated successfully',
  };
};

export const updateEmployee = async (
  id: string,
  updateData: any,
): Promise<ApiResponse<Employee>> => {
  await mockDelay();

  const employeeIndex = mockEmployees.findIndex((emp) => emp.id === id);
  if (employeeIndex === -1) {
    throw new Error('Employee not found');
  }

  // Update employee data
  mockEmployees[employeeIndex] = {
    ...mockEmployees[employeeIndex],
    ...updateData,
  };

  return {
    data: mockEmployees[employeeIndex],
    success: true,
    message: 'Employee updated successfully',
  };
};

// Cash Register APIs
export const fetchCashRegisters = async (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  location?: string;
}): Promise<ApiResponse<PaginatedResponse<CashRegister>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, status, location } = params || {};

  let filteredRegisters = mockCashRegisters;

  if (status) {
    filteredRegisters = filteredRegisters.filter(
      (register) => register.status === status,
    );
  }

  if (location) {
    filteredRegisters = filteredRegisters.filter((register) =>
      register.location.toLowerCase().includes(location.toLowerCase()),
    );
  }

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedRegisters = filteredRegisters.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedRegisters,
      total: filteredRegisters.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Cash registers fetched successfully',
  };
};

export const openCashRegister = async (
  registerId: string,
  employeeId: string,
  openingBalance: number,
): Promise<ApiResponse<CashRegister>> => {
  await mockDelay();

  const registerIndex = mockCashRegisters.findIndex(
    (reg) => reg.id === registerId,
  );

  if (registerIndex === -1) {
    throw new Error('Cash register not found');
  }

  const employee = mockEmployees.find((emp) => emp.id === employeeId);
  if (!employee) {
    throw new Error('Employee not found');
  }

  mockCashRegisters[registerIndex] = {
    ...mockCashRegisters[registerIndex],
    status: 'open',
    openingBalance,
    currentBalance: openingBalance,
    employeeId,
    employeeName: employee.name,
    openedAt: new Date().toISOString(),
    shiftId: `SHIFT${Date.now()}`,
  };

  return {
    data: mockCashRegisters[registerIndex],
    success: true,
    message: 'Cash register opened successfully',
  };
};

export const closeCashRegister = async (
  registerId: string,
  closingBalance: number,
): Promise<ApiResponse<CashRegister>> => {
  await mockDelay();

  const registerIndex = mockCashRegisters.findIndex(
    (reg) => reg.id === registerId,
  );

  if (registerIndex === -1) {
    throw new Error('Cash register not found');
  }

  mockCashRegisters[registerIndex] = {
    ...mockCashRegisters[registerIndex],
    status: 'closed',
    currentBalance: closingBalance,
    closedAt: new Date().toISOString(),
  };

  return {
    data: mockCashRegisters[registerIndex],
    success: true,
    message: 'Cash register closed successfully',
  };
};

// Transaction APIs
export const fetchTransactions = async (params?: {
  page?: number;
  pageSize?: number;
  registerId?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
}): Promise<ApiResponse<PaginatedResponse<Transaction>>> => {
  await mockDelay();

  const {
    page = 1,
    pageSize = 10,
    registerId,
    type,
    dateFrom,
    dateTo,
  } = params || {};

  let filteredTransactions = mockTransactions;

  if (registerId) {
    filteredTransactions = filteredTransactions.filter(
      (txn) => txn.registerId === registerId,
    );
  }

  if (type) {
    filteredTransactions = filteredTransactions.filter(
      (txn) => txn.type === type,
    );
  }

  if (dateFrom) {
    filteredTransactions = filteredTransactions.filter(
      (txn) => new Date(txn.timestamp) >= new Date(dateFrom),
    );
  }

  if (dateTo) {
    filteredTransactions = filteredTransactions.filter(
      (txn) => new Date(txn.timestamp) <= new Date(dateTo),
    );
  }

  // Sort by timestamp descending
  filteredTransactions.sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
  );

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedTransactions = filteredTransactions.slice(
    startIndex,
    endIndex,
  );

  return {
    data: {
      data: paginatedTransactions,
      total: filteredTransactions.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Transactions fetched successfully',
  };
};

export const createTransaction = async (
  transactionData: Omit<Transaction, 'id' | 'timestamp'>,
): Promise<ApiResponse<Transaction>> => {
  await mockDelay();

  const newTransaction: Transaction = {
    id: `TXN${Date.now()}`,
    ...transactionData,
    timestamp: new Date().toISOString(),
  };

  mockTransactions.unshift(newTransaction);

  // Update register balance
  const registerIndex = mockCashRegisters.findIndex(
    (reg) => reg.id === transactionData.registerId,
  );
  if (registerIndex !== -1) {
    mockCashRegisters[registerIndex].currentBalance += transactionData.amount;
  }

  return {
    data: newTransaction,
    success: true,
    message: 'Transaction created successfully',
  };
};

// Store APIs
export const fetchStores = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  location?: string;
}): Promise<ApiResponse<PaginatedResponse<Store>>> => {
  await mockDelay();

  const {
    page = 1,
    pageSize = 10,
    search = '',
    status,
    location,
  } = params || {};

  let filteredStores = mockStores;

  if (search) {
    filteredStores = filteredStores.filter(
      (store) =>
        store.name.toLowerCase().includes(search.toLowerCase()) ||
        store.address.toLowerCase().includes(search.toLowerCase()),
    );
  }

  if (status) {
    filteredStores = filteredStores.filter((store) => store.status === status);
  }

  if (location) {
    filteredStores = filteredStores.filter((store) =>
      store.location.toLowerCase().includes(location.toLowerCase()),
    );
  }

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedStores = filteredStores.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedStores,
      total: filteredStores.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Stores fetched successfully',
  };
};

export const createStore = async (
  storeData: CreateStoreRequest,
): Promise<ApiResponse<Store>> => {
  await mockDelay();

  const manager = mockEmployees.find((emp) => emp.id === storeData.managerId);
  if (!manager) {
    throw new Error('Manager not found');
  }

  const newStore: Store = {
    id: `STORE${Date.now()}`,
    ...storeData,
    managerName: manager.name,
    status: 'active',
    totalRevenue: 0,
    monthlyRevenue: 0,
    employeeCount: 1,
  };

  mockStores.unshift(newStore);

  return {
    data: newStore,
    success: true,
    message: 'Store created successfully',
  };
};

export const updateStoreStatus = async (
  id: string,
  status: Store['status'],
): Promise<ApiResponse<Store>> => {
  await mockDelay();

  const storeIndex = mockStores.findIndex((store) => store.id === id);

  if (storeIndex === -1) {
    throw new Error('Store not found');
  }

  mockStores[storeIndex].status = status;

  return {
    data: mockStores[storeIndex],
    success: true,
    message: 'Store status updated successfully',
  };
};

// Product APIs
export const fetchProducts = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  category?: string;
  storeId?: string;
  lowStock?: boolean;
}): Promise<ApiResponse<PaginatedResponse<Product>>> => {
  await mockDelay();

  const {
    page = 1,
    pageSize = 10,
    search = '',
    category,
    storeId,
    lowStock,
  } = params || {};

  let filteredProducts = mockProducts;

  if (search) {
    filteredProducts = filteredProducts.filter(
      (product) =>
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.category.toLowerCase().includes(search.toLowerCase()),
    );
  }

  if (category) {
    filteredProducts = filteredProducts.filter(
      (product) => product.category === category,
    );
  }

  if (storeId) {
    filteredProducts = filteredProducts.filter(
      (product) => product.storeId === storeId,
    );
  }

  if (lowStock) {
    filteredProducts = filteredProducts.filter(
      (product) => product.stockLevel < product.minStockLevel,
    );
  }

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedProducts,
      total: filteredProducts.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Products fetched successfully',
  };
};

export const updateProductStock = async (
  id: string,
  newStockLevel: number,
): Promise<ApiResponse<Product>> => {
  await mockDelay();

  const productIndex = mockProducts.findIndex((product) => product.id === id);

  if (productIndex === -1) {
    throw new Error('Product not found');
  }

  mockProducts[productIndex].stockLevel = newStockLevel;
  mockProducts[productIndex].lastRestocked = new Date().toISOString();

  return {
    data: mockProducts[productIndex],
    success: true,
    message: 'Product stock updated successfully',
  };
};

// Shift APIs
export const fetchShifts = async (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  employeeId?: string;
  registerId?: string;
}): Promise<ApiResponse<PaginatedResponse<Shift>>> => {
  await mockDelay();

  const {
    page = 1,
    pageSize = 10,
    status,
    employeeId,
    registerId,
  } = params || {};

  let filteredShifts = mockShifts;

  if (status) {
    filteredShifts = filteredShifts.filter((shift) => shift.status === status);
  }

  if (employeeId) {
    filteredShifts = filteredShifts.filter(
      (shift) => shift.employeeId === employeeId,
    );
  }

  if (registerId) {
    filteredShifts = filteredShifts.filter(
      (shift) => shift.registerId === registerId,
    );
  }

  // Sort by start time descending
  filteredShifts.sort(
    (a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime(),
  );

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedShifts = filteredShifts.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedShifts,
      total: filteredShifts.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Shifts fetched successfully',
  };
};

export const endShift = async (
  id: string,
  closingBalance: number,
): Promise<ApiResponse<Shift>> => {
  await mockDelay();

  const shiftIndex = mockShifts.findIndex((shift) => shift.id === id);

  if (shiftIndex === -1) {
    throw new Error('Shift not found');
  }

  mockShifts[shiftIndex] = {
    ...mockShifts[shiftIndex],
    endTime: new Date().toISOString(),
    closingBalance,
    status: 'completed',
  };

  return {
    data: mockShifts[shiftIndex],
    success: true,
    message: 'Shift ended successfully',
  };
};

export interface ExchangeRateProps {
  fiatCode?: string;
  orderBy?: 'spotPrice' | 'askPrice' | 'bidPrice';
  orderByDescending?: boolean;
}

export const getExchangeRates = async (
  params?: ExchangeRateProps,
): Promise<ApiResponse<Array<ExchangeRate>>> => {
  await mockDelay();

  const { fiatCode = '', orderBy, orderByDescending } = params || {};

  let filteredExchangeRates = [...mockExchangeRates];

  if (fiatCode) {
    filteredExchangeRates = filteredExchangeRates.filter((exchangeRate) =>
      exchangeRate.fiatCode.toLowerCase().includes(fiatCode.toLowerCase()),
    );
  }

  if (orderBy) {
    filteredExchangeRates = filteredExchangeRates.sort(
      (a, b) => a[orderBy] - b[orderBy],
    );
  }

  if (orderByDescending) {
    filteredExchangeRates = filteredExchangeRates.reverse();
  }

  return {
    data: filteredExchangeRates,
    success: true,
    message: 'Exchange Rates fetched successfully',
  };
};

export const updateExchangeRate = async (params: {
  exchangeRate: ExchangeRate;
}): Promise<ApiResponse<ExchangeRate>> => {
  await mockDelay();

  const { exchangeRate } = params || {};

  const existRate = mockExchangeRates.findIndex(
    (rate) => rate.fiatCode === exchangeRate.fiatCode,
  );

  if (existRate === -1) throw new Error('Exchange Rate not found');

  mockExchangeRates[existRate] = exchangeRate;

  return {
    data: exchangeRate,
    success: true,
    message: 'Exchange Rate updated successfully',
  };
};
