import React from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Typography,
  Card,
  Row,
  Col,
  Avatar,
  Divider,
  Badge,
  Space,
  Button,
  Table,
  QRCode,
  Steps,
  Timeline,
  Alert,
} from 'antd';
import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  CalendarOutlined,
  CreditCardOutlined,
  QrcodeOutlined,
  CloseOutlined,
  EditOutlined,
  PrinterOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  FileTextOutlined,
  TagOutlined,
  BankOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { formatDateTime, formatCurrency } from '@/utils/tableUtils.ts';
import type { Order, OrderItem } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface OrderDetailsModalProps {
  visible: boolean;
  order: Order | null;
  onClose: () => void;
  onEdit?: (order: Order) => void;
  onUpdateStatus?: (orderId: string, status: Order['status']) => void;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = ({
  visible,
  order,
  onClose,
  onEdit,
  // @ts-ignore
  onUpdateStatus,
}) => {
  const { t } = useTranslation();

  if (!order) return null;

  const getPaymentStatusColor = (status: Order['paymentStatus']) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'processing':
        return 'blue';
      case 'pending':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const getPaymentMethodIcon = (method?: string) => {
    switch (method) {
      case 'cash':
        return <DollarOutlined />;
      case 'card':
        return <CreditCardOutlined />;
      case 'digital_wallet':
        return <BankOutlined />;
      case 'crypto':
        return <QrcodeOutlined />;
      default:
        return <DollarOutlined />;
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className='text-green-600' />;
      case 'pending':
        return <ClockCircleOutlined className='text-orange-600' />;
      case 'failed':
        return <CloseCircleOutlined className='text-red-600' />;
      case 'cancelled':
        return <ExclamationCircleOutlined className='text-gray-600' />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getCurrentStep = () => {
    switch (order.status) {
      case 'pending':
        return 0;
      case 'completed':
        return 2;
      case 'failed':
      case 'cancelled':
        return 1;
      default:
        return 0;
    }
  };

  const getStepStatus = (stepIndex: number) => {
    const currentStep = getCurrentStep();
    if (stepIndex < currentStep) return 'finish';
    if (stepIndex === currentStep) {
      if (order.status === 'failed' || order.status === 'cancelled')
        return 'error';
      return 'process';
    }
    return 'wait';
  };

  const itemColumns = [
    {
      title: t('orders.itemName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: OrderItem) => (
        <div>
          <div className='font-medium text-gray-900'>{name}</div>
          {record.category && <Tag className='mt-1'>{record.category}</Tag>}
        </div>
      ),
    },
    {
      title: t('orders.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number) => (
        <span className='font-medium'>{quantity}</span>
      ),
    },
    {
      title: t('orders.unitPrice'),
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 120,
      render: (price: number) => (
        <span className='text-gray-600'>{formatCurrency(price)}</span>
      ),
    },
    {
      title: t('orders.totalPrice'),
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      render: (total: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(total)}
        </span>
      ),
    },
  ];

  const handleCopyQRCode = () => {
    if (order.qrCode) {
      navigator.clipboard.writeText(order.qrCode);
      // You could add a success message here
    }
  };

  const timelineItems = [
    {
      color: 'blue',
      children: (
        <div>
          <div className='font-medium'>Order Created</div>
          <div className='text-sm text-gray-500'>
            {formatDateTime(order.date)}
          </div>
          {order.createdBy && (
            <div className='text-xs text-gray-400'>by {order.createdBy}</div>
          )}
        </div>
      ),
    },
    ...(order.paymentStatus === 'processing'
      ? [
          {
            color: 'orange' as const,
            children: (
              <div>
                <div className='font-medium'>Payment Processing</div>
                <div className='text-sm text-gray-500'>
                  Payment is being processed
                </div>
              </div>
            ),
          },
        ]
      : []),
    ...(order.status === 'completed'
      ? [
          {
            color: 'green' as const,
            children: (
              <div>
                <div className='font-medium'>Order Completed</div>
                <div className='text-sm text-gray-500'>
                  {formatDateTime(order.updatedAt || order.date)}
                </div>
              </div>
            ),
          },
        ]
      : []),
    ...(order.status === 'failed' || order.status === 'cancelled'
      ? [
          {
            color: 'red' as const,
            children: (
              <div>
                <div className='font-medium'>
                  {order.status === 'failed'
                    ? 'Order Failed'
                    : 'Order Cancelled'}
                </div>
                <div className='text-sm text-gray-500'>
                  {formatDateTime(order.updatedAt || order.date)}
                </div>
                {order.refundReason && (
                  <div className='text-xs text-gray-400'>
                    Reason: {order.refundReason}
                  </div>
                )}
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className='top-8'
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
    >
      <div className='p-6'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center gap-4'>
            <Avatar
              size={64}
              className='bg-gradient-to-br from-green-500 to-emerald-600 text-white font-bold text-xl'
              icon={<ShoppingCartOutlined />}
            />
            <div>
              <div className='flex items-center gap-3'>
                <Title level={3} className='text-gray-900 mb-0 font-bold'>
                  {t('orders.orderDetails')} #{order.id}
                </Title>
                <Badge
                  status={
                    order.status === 'completed'
                      ? 'success'
                      : order.status === 'pending'
                        ? 'processing'
                        : 'error'
                  }
                  text={
                    <span
                      className={`font-medium ${
                        order.status === 'completed'
                          ? 'text-green-600'
                          : order.status === 'pending'
                            ? 'text-orange-600'
                            : 'text-red-600'
                      }`}
                    >
                      {t(`orders.statuses.${order.status}`)}
                    </span>
                  }
                />
              </div>
              <div className='flex items-center gap-2 mt-1'>
                <Text className='text-gray-600'>{order.description}</Text>
              </div>
            </div>
          </div>

          <Space>
            <Button
              icon={<PrinterOutlined />}
              onClick={() => window.print()}
              className='hover:bg-gray-100'
            >
              {t('common.print')}
            </Button>
            {onEdit && (
              <Button
                type='primary'
                icon={<EditOutlined />}
                onClick={() => onEdit(order)}
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('common.edit')}
              </Button>
            )}
          </Space>
        </div>

        <Divider className='my-6' />

        {/* Order Progress */}
        <Card
          className='mb-6'
          title={
            <span className='flex items-center gap-2'>
              <ClockCircleOutlined className='text-blue-600' />
              {t('orders.orderProgress')}
            </span>
          }
        >
          <Steps current={getCurrentStep()} className='mb-4'>
            <Step
              title={t('orders.orderCreated')}
              status={getStepStatus(0)}
              icon={getStatusIcon('pending')}
            />
            <Step
              title={t('orders.paymentProcessing')}
              status={getStepStatus(1)}
              icon={getStatusIcon(
                order.paymentStatus === 'processing' ? 'pending' : order.status,
              )}
            />
            <Step
              title={t('orders.orderCompleted')}
              status={getStepStatus(2)}
              icon={getStatusIcon(order.status)}
            />
          </Steps>
          <div className='pt-8 px-2'>
            <Timeline items={timelineItems} />
          </div>
        </Card>

        {/* Order Information */}
        <Row gutter={[24, 24]} className='mb-6'>
          <Col xs={24} lg={16}>
            <Card
              title={
                <span className='flex items-center gap-2'>
                  <FileTextOutlined className='text-blue-600' />
                  {t('orders.orderInformation')}
                </span>
              }
            >
              <Descriptions column={2} size='middle'>
                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <UserOutlined className='text-gray-500' />
                      {t('orders.member')}
                    </span>
                  }
                >
                  <span className='font-medium'>{order.memberName}</span>
                  <div className='text-sm text-gray-500'>
                    ID: {order.memberId}
                  </div>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <DollarOutlined className='text-gray-500' />
                      {t('orders.amount')}
                    </span>
                  }
                >
                  <span className='font-bold text-green-600 text-lg'>
                    {formatCurrency(order.amount)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <CalendarOutlined className='text-gray-500' />
                      {t('orders.date')}
                    </span>
                  }
                >
                  {formatDateTime(order.date)}
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <TagOutlined className='text-gray-500' />
                      {t('orders.paymentStatus')}
                    </span>
                  }
                >
                  <Tag
                    color={getPaymentStatusColor(order.paymentStatus)}
                    className='font-medium'
                  >
                    {t(`orders.paymentStatuses.${order.paymentStatus}`)}
                  </Tag>
                </Descriptions.Item>

                {order.paymentMethod && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        {getPaymentMethodIcon(order.paymentMethod)}
                        {t('orders.paymentMethod')}
                      </span>
                    }
                  >
                    {t(`orders.paymentMethods.${order.paymentMethod}`)}
                  </Descriptions.Item>
                )}

                {order.transactionId && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <BankOutlined className='text-gray-500' />
                        {t('orders.transactionId')}
                      </span>
                    }
                  >
                    <span className='font-mono bg-gray-100 px-2 py-1 rounded text-sm'>
                      {order.transactionId}
                    </span>
                  </Descriptions.Item>
                )}
              </Descriptions>

              {order.notes && (
                <div className='mt-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <FileTextOutlined className='text-gray-500' />
                    <Text className='font-medium text-gray-700'>
                      {t('orders.notes')}
                    </Text>
                  </div>
                  <Paragraph className='text-gray-600 ml-6 mb-0'>
                    {order.notes}
                  </Paragraph>
                </div>
              )}
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            {/* QR Code */}
            {order.qrCode && (
              <Card
                title={
                  <span className='flex items-center gap-2'>
                    <QrcodeOutlined className='text-blue-600' />
                    {t('orders.qrCode')}
                  </span>
                }
                className='mb-4'
              >
                <div className='text-center flex flex-col items-center'>
                  <QRCode value={order.qrCode} className='mb-4' />
                  <div className='text-sm text-gray-600 mb-3'>
                    {order.qrCode}
                  </div>
                  <Button
                    icon={<CopyOutlined />}
                    onClick={handleCopyQRCode}
                    size='small'
                    className='w-full'
                  >
                    {t('orders.copyQRCode')}
                  </Button>
                </div>
              </Card>
            )}
          </Col>
        </Row>
        <Row gutter={[24, 24]} className='mb-6'>
          <Col xs={24} lg={24}>
            {/* Refund Information */}
            {order.refundAmount && (
              <Alert
                message={t('orders.refundProcessed')}
                description={
                  <div>
                    <div className='font-medium'>
                      {formatCurrency(order.refundAmount)}
                    </div>
                    {order.refundReason && (
                      <div className='text-sm mt-1'>
                        Reason: {order.refundReason}
                      </div>
                    )}
                  </div>
                }
                type='info'
                showIcon
                className='mb-4'
              />
            )}
          </Col>
        </Row>
        {/* Order Items */}
        {order.items && order.items.length > 0 && (
          <Card
            title={
              <span className='flex items-center gap-2'>
                <ShoppingCartOutlined className='text-blue-600' />
                {t('orders.orderItems')}
              </span>
            }
            extra={
              <Text className='text-gray-500'>
                {order.items.length}{' '}
                {order.items.length === 1 ? 'item' : 'items'}
              </Text>
            }
          >
            <Table
              dataSource={order.items}
              columns={itemColumns}
              pagination={false}
              rowKey='id'
              size='small'
              summary={() => (
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={3}>
                    <Text className='font-medium'>{t('orders.total')}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    <Text className='font-bold text-green-600 text-lg'>
                      {formatCurrency(order.amount)}
                    </Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default OrderDetailsModal;
