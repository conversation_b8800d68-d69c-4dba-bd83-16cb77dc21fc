import React, { useState } from 'react';
import {
  Select,
  Space,
  Tag,
  Button,
  Alert,
  Card,
  Typography,
  Avatar,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useOrders, useUpdateOrderStatus } from '@/hooks/useOrders.ts';
import {
  formatDate,
  formatCurrency,
  getStatusColor,
} from '@/utils/tableUtils.ts';
import GalaxyTable from '@/components/GalaxyTable';
import OrderDetailsModal from '@/pages/Orders/modals/OrderDetailsModal';
import type { Order } from '@/types';
import Search from 'antd/es/input/Search';

const { Option } = Select;
const { Title, Text } = Typography;

const Orders: React.FC = () => {
  const { t } = useTranslation();
  // @ts-ignore
  const [statusFilter, setStatusFilter] = useState<string>('');
  // @ts-ignore
  const [searchTerm, setSearchTerm] = useState('');
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  const {
    data: ordersResponse,
    isLoading,
    error,
    refetch,
  } = useOrders({
    page: pagination.current,
    pageSize: pagination.pageSize,
    status: statusFilter || undefined,
    memberId: searchTerm || undefined,
  });

  const updateOrderStatusMutation = useUpdateOrderStatus();

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowDetailsModal(true);
  };

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedOrder(null);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handleStatusUpdate = async (
    orderId: string,
    newStatus: Order['status'],
  ) => {
    try {
      await updateOrderStatusMutation.mutateAsync({
        id: orderId,
        status: newStatus,
      });
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation
    }
  };

  const columns = [
    {
      title: 'Order Details',
      dataIndex: 'id',
      key: 'orderDetails',
      render: (id: string, record: Order) => (
        <div className='flex items-center gap-3'>
          <Avatar
            className='bg-gradient-to-br from-green-400 to-green-600 text-white font-medium'
            icon={<ShoppingCartOutlined />}
            size={40}
          />
          <div>
            <Text className='font-medium text-gray-900 block'>{id}</Text>
            <Text className='text-sm text-gray-500'>{record.memberName}</Text>
          </div>
        </div>
      ),
    },
    {
      title: t('orders.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <div className='text-right'>
          <Text className='font-bold text-lg text-gray-900 block'>
            {formatCurrency(amount)}
          </Text>
          <Text className='text-sm text-gray-500'>Transaction</Text>
        </div>
      ),
      sorter: (a: Order, b: Order) => a.amount - b.amount,
    },
    {
      title: t('orders.status'),
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: t('orders.pending'), value: 'pending' },
        { text: t('orders.completed'), value: 'completed' },
        { text: t('orders.failed'), value: 'failed' },
        { text: t('orders.cancelled'), value: 'cancelled' },
      ],
      onFilter: (value: any, record: Order) => {
        return record.status === value;
      },
      render: (status: string, record: Order) => (
        <Select
          value={status as Order['status']}
          style={{ width: 140 }}
          onChange={(newStatus: Order['status']) =>
            handleStatusUpdate(record.id, newStatus)
          }
          loading={updateOrderStatusMutation.isPending}
          className='rounded-lg'
        >
          <Option value='pending'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
              {t('orders.pending')}
            </div>
          </Option>
          <Option value='completed'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-green-400 rounded-full'></div>
              {t('orders.completed')}
            </div>
          </Option>
          <Option value='failed'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-red-400 rounded-full'></div>
              {t('orders.failed')}
            </div>
          </Option>
          <Option value='cancelled'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
              {t('orders.cancelled')}
            </div>
          </Option>
        </Select>
      ),
    },
    {
      title: t('orders.paymentStatus'),
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (paymentStatus: string) => (
        <Tag
          color={getStatusColor(paymentStatus)}
          className='rounded-full px-3 py-1 font-medium'
        >
          {t(`orders.${paymentStatus}`)}
        </Tag>
      ),
    },
    {
      title: t('orders.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => (
        <div>
          <Text className='text-gray-900 block'>
            {formatDate(date).split(',')[0]}
          </Text>
          <Text className='text-sm text-gray-500'>
            {formatDate(date).split(',')[1]}
          </Text>
        </div>
      ),
      sorter: (a: Order, b: Order) =>
        new Date(a.date).getTime() - new Date(b.date).getTime(),
    },
    {
      title: t('orders.qrCode'),
      dataIndex: 'qrCode',
      key: 'qrCode',
      render: (qrCode: string) => (
        <div className='font-mono text-sm bg-gray-50 px-3 py-1 rounded-lg inline-block'>
          {qrCode}
        </div>
      ),
    },
    {
      title: t('orders.actions'),
      key: 'actions',
      width: 120,
      render: (_: any, record: Order) => (
        <Space>
          <Button
            type='text'
            icon={<EyeOutlined />}
            className='hover:bg-primary/10 hover:text-primary rounded-lg'
            onClick={() => handleViewOrder(record)}
          >
            View
          </Button>
        </Space>
      ),
    },
  ];

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load orders'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const orders = ordersResponse?.data?.data || [];
  const total = ordersResponse?.data?.total || 0;
  const completedOrders = orders.filter((o) => o.status === 'completed').length;
  const pendingOrders = orders.filter((o) => o.status === 'pending').length;
  const totalRevenue = orders
    .filter((o) => o.status === 'completed')
    .reduce((sum, o) => sum + o.amount, 0);

  return (
    <div className='space-y-8'>
      {/* Header Section */}
      <div className='bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <div className='w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center'>
              <ShoppingCartOutlined className='text-white text-xl' />
            </div>
            <div>
              <Title level={2} className='text-gray-900 mb-1 font-bold'>
                {t('orders.title')}
              </Title>
              <Text className='text-gray-600 text-base'>
                Track and manage casino transactions and payments
              </Text>
            </div>
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
            size='large'
            className='rounded-lg h-12 px-6 font-medium shadow-lg hover:shadow-xl transition-all'
          >
            {t('common.refresh')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center'>
            <Statistic
              title='Total Orders'
              value={total}
              prefix={<ShoppingCartOutlined className='text-primary' />}
              valueStyle={{
                color: '#1890ff',
                fontSize: '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center'>
            <Statistic
              title='Completed'
              value={completedOrders}
              prefix={<CheckCircleOutlined className='text-green-500' />}
              valueStyle={{
                color: '#52c41a',
                fontSize: '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center'>
            <Statistic
              title='Pending'
              value={pendingOrders}
              prefix={<ClockCircleOutlined className='text-orange-500' />}
              valueStyle={{
                color: '#faad14',
                fontSize: '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className='rounded-2xl border-0 shadow-card text-center'>
            <Statistic
              title='Revenue'
              value={totalRevenue}
              prefix={<DollarOutlined className='text-purple-500' />}
              valueStyle={{
                color: '#722ed1',
                fontSize: '28px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
          </Card>
        </Col>
      </Row>

      {/* Search and Filters */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
          <div className='flex items-center gap-3'>
            <SearchOutlined className='text-gray-400 text-lg' />
            <div>
              <Text className='font-medium text-gray-900 block'>
                Search Orders
              </Text>
              <Text className='text-sm text-gray-500'>
                Find Orders by Member's name or MemberID
              </Text>
            </div>
          </div>
          <div className='flex gap-2'>
            <Select
              placeholder='All Statuses'
              allowClear
              style={{ width: 200 }}
              value={statusFilter || undefined}
              onChange={handleStatusFilter}
              size='large'
              className='rounded-lg'
            >
              <Option value='pending'>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
                  {t('orders.pending')}
                </div>
              </Option>
              <Option value='completed'>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                  {t('orders.completed')}
                </div>
              </Option>
              <Option value='failed'>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                  {t('orders.failed')}
                </div>
              </Option>
              <Option value='cancelled'>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
                  {t('orders.cancelled')}
                </div>
              </Option>
            </Select>
            <Search
              placeholder="Search by Member's name or MemberID..."
              allowClear
              size='large'
              style={{ width: 420 }}
              onSearch={handleSearch}
              className='rounded-lg'
            />
          </div>
        </div>
      </Card>
      {/* Orders Table */}
      <Card className='rounded-2xl border-0 shadow-card overflow-hidden'>
        <GalaxyTable
          data={orders}
          columns={columns}
          loading={isLoading}
          customSort={true}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            onChange: handlePaginationChange,
          }}
          rowKey='id'
        />
      </Card>

      {/* Order Details Modal */}
      <OrderDetailsModal
        visible={showDetailsModal}
        order={selectedOrder}
        onClose={handleCloseDetailsModal}
        onEdit={(order) => {
          // TODO: Implement edit functionality
          console.log('Edit order:', order);
        }}
        onUpdateStatus={(orderId, status) => {
          // TODO: Implement status update
          console.log('Update status:', orderId, status);
        }}
      />
    </div>
  );
};

export default Orders;
