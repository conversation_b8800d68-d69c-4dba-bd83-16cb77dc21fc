import { mockOrders, mockDelay } from '@/api/mockData.ts';
import type { Order, ApiResponse, PaginatedResponse } from '@/types';

// Mock API functions - replace with real API calls later
export const fetchOrders = async (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  memberId?: string;
}): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, status, memberId } = params || {};

  // Filter orders based on parameters
  let filteredOrders = mockOrders;
  if (status) {
    filteredOrders = filteredOrders.filter((order) => order.status === status);
  }

  if (memberId) {
    filteredOrders = filteredOrders.filter(
      (order) =>
        order.memberId === memberId || order.memberName.includes(memberId),
    );
  }

  // Sort by date (newest first)
  filteredOrders.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
  );

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  // Later replace with: return api.get<PaginatedResponse<Order>>('/orders', params);
  return {
    data: {
      data: paginatedOrders,
      total: filteredOrders.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Orders fetched successfully',
  };
};

export const fetchOrderById = async (
  id: string,
): Promise<ApiResponse<Order>> => {
  await mockDelay();

  const order = mockOrders.find((o) => o.id === id);

  if (!order) {
    throw new Error('Order not found');
  }

  // Later replace with: return api.get<Order>(`/orders/${id}`);
  return {
    data: order,
    success: true,
    message: 'Order fetched successfully',
  };
};

export const updateOrderStatus = async (
  id: string,
  status: Order['status'],
): Promise<ApiResponse<Order>> => {
  await mockDelay();

  const orderIndex = mockOrders.findIndex((o) => o.id === id);

  if (orderIndex === -1) {
    throw new Error('Order not found');
  }

  // Update order status in mock data
  mockOrders[orderIndex] = {
    ...mockOrders[orderIndex],
    status,
    paymentStatus:
      status === 'completed'
        ? 'completed'
        : mockOrders[orderIndex].paymentStatus,
  };

  // Later replace with: return api.put<Order>(`/orders/${id}/status`, { status });
  return {
    data: mockOrders[orderIndex],
    success: true,
    message: 'Order status updated successfully',
  };
};
