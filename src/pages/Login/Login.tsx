import { StarFilled } from '@ant-design/icons';
import { Form, Input, Button, Typography, Card, Select } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { mockAdminAccount } from '@/api/mockData';
import { ROUTES } from '@/constants/routes';
import { useLogin } from '@/hooks/useLogin';
import { useAuthStore } from '@/stores';
import type { Language, LoginProps } from '@/types';

const { Title } = Typography;
const { Option } = Select;

export default function Login() {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  const { mutate: login, isPending: loggingIn } = useLogin({
    onError: (error: Error) => {
      setErrorMessage(error.message);
    },
  });

  const isDev = import.meta.env.DEV;

  const [errorMessage, setErrorMessage] = useState('');

  const onFinish = (values: LoginProps) => {
    login({ loginProps: values });
  };
  const handleLanguageChange = (language: Language) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
  };

  useEffect(() => {
    if (isAuthenticated) {
      navigate(ROUTES.DASHBOARD, { replace: true });
    }
  }, [isAuthenticated, navigate]);

  return (
    <div className='min-h-screen flex flex-col items-center pt-16 sm:pt-32 px-4 gap-y-8 bg-gray-100'>
      <Card className='w-96 max-w-full'>
        <div className='w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mx-auto'>
          <StarFilled className='text-white text-lg' />
        </div>

        <Title level={3} className='!my-4 text-center'>
          {t('login.title')}
        </Title>

        <Form
          form={form}
          name='loginForm'
          layout='vertical'
          onFinish={onFinish}
          autoComplete='off'
          initialValues={{
            username: isDev ? mockAdminAccount.username : '',
            password: isDev ? mockAdminAccount.password : '',
          }}
        >
          <Form.Item
            label={t('login.username')}
            name='username'
            rules={[{ required: true, message: t('login.usernameError') }]}
          >
            <Input
              placeholder={t('login.usernamePlaceholder')}
              autoFocus
              size='large'
              onChange={() => {
                setErrorMessage('');
              }}
            />
          </Form.Item>

          <Form.Item
            label={t('login.password')}
            name='password'
            rules={[{ required: true, message: t('login.passwordError') }]}
          >
            <Input.Password
              placeholder={t('login.passwordPlaceholder')}
              size='large'
              onChange={() => {
                setErrorMessage('');
              }}
            />
          </Form.Item>

          {errorMessage && (
            <div className='mb-4 text-red-400'>{errorMessage}</div>
          )}

          {isDev && (
            <div className='flex gap-x-sm mb-4'>
              <Button
                size='small'
                onClick={() => {
                  form.setFieldsValue({
                    username: mockAdminAccount.username,
                    password: mockAdminAccount.password,
                  });
                }}
              >
                admin
              </Button>
            </div>
          )}

          <Form.Item>
            <Button
              type='primary'
              htmlType='submit'
              size='large'
              block
              loading={loggingIn}
            >
              {t('login.logIn')}
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <div>
        <Select
          value={i18n.language as Language}
          onChange={handleLanguageChange}
          className='w-full'
          size='middle'
          variant='borderless'
        >
          <Option value='en'>
            <div className='flex items-center gap-2'>
              <span className='text-base'>🇺🇸</span>
              {t('language.english')}
            </div>
          </Option>
          <Option value='tw'>
            <div className='flex items-center gap-2'>
              <span className='text-base'>🇹🇼</span>
              {t('language.taiwan')}
            </div>
          </Option>
        </Select>
      </div>
    </div>
  );
}
