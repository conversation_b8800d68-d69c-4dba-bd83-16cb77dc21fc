import React from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Tooltip,
  <PERSON>confirm,
  Badge,
  Typography,
  Avatar,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  KeyOutlined,
  StopOutlined,
  PlayCircleOutlined,
  CrownOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUpdateRoleStatus, useDeleteRole } from '@/hooks/useRoles.ts';
import { formatDate } from '@/utils/tableUtils.ts';
import type { Role } from '@/types';

const { Text } = Typography;

interface RolesListProps {
  roles: Role[];
  loading: boolean;
  // @ts-ignore
  onEdit: (role: Role) => void;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    // @ts-ignore
    onChange: (page: number, pageSize: number) => void;
  };
}

const RolesList: React.FC<RolesListProps> = ({
  roles,
  loading,
  onEdit,
  pagination,
}) => {
  const { t } = useTranslation();
  const { isMobile, isTablet } = useResponsive();
  const updateStatusMutation = useUpdateRoleStatus();
  const deleteRoleMutation = useDeleteRole();

  const handleToggleStatus = async (role: Role) => {
    const newStatus = role.status === 'active' ? 'inactive' : 'active';
    updateStatusMutation.mutate({ id: role.id, status: newStatus });
  };

  const handleDeleteRole = async (roleId: string) => {
    deleteRoleMutation.mutate(roleId);
  };

  const getRoleIcon = (role: Role) => {
    if (role.isSystem) {
      return <CrownOutlined className='text-yellow-600' />;
    }
    return <SafetyOutlined className='text-blue-600' />;
  };

  const columns = [
    {
      title: t('rolePermission.roleName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Role) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            size={isMobile ? 32 : 40}
            className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
            icon={getRoleIcon(record)}
          />
          <div className='min-w-0 flex-1'>
            <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
              <Text className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}>{name}</Text>
              {record.isSystem && (
                <Tag color='gold' className={isMobile ? 'text-xs' : ''}>{t('rolePermission.system')}</Tag>
              )}
            </div>
            {!isMobile && (
              <Text className='text-sm text-gray-500'>{record.description}</Text>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('rolePermission.status'),
      dataIndex: 'status',
      key: 'status',
      width: isMobile ? 80 : 120,
      render: (status: Role['status']) => (
        <Badge
          status={status === 'active' ? 'success' : 'error'}
          text={
            <span
              className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'} ${
                status === 'active' ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {isMobile ? (status === 'active' ? 'Active' : 'Inactive') : t(`rolePermission.${status}`)}
            </span>
          }
        />
      ),
    },
    {
      title: t('rolePermission.permissions'),
      dataIndex: 'permissions',
      key: 'permissions',
      width: isMobile ? 80 : 150,
      render: (permissions: string[]) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <KeyOutlined className={`text-blue-500 ${isMobile ? 'text-sm' : ''}`} />
          <span className={`font-medium text-blue-600 ${isMobile ? 'text-sm' : ''}`}>
            {permissions.length}
          </span>
          {!isMobile && (
            <span className='text-gray-500 text-sm'>
              {t('rolePermission.permissionsCount')}
            </span>
          )}
        </div>
      ),
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('rolePermission.assignedUsers'),
      dataIndex: 'userCount',
      key: 'userCount',
      width: isMobile ? 80 : 150,
      render: (userCount: number) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <UserOutlined className={`text-green-500 ${isMobile ? 'text-sm' : ''}`} />
          <span className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}>{userCount}</span>
          {!isMobile && (
            <span className='text-gray-500 text-sm'>
              {userCount === 1
                ? t('rolePermission.user')
                : t('rolePermission.users')}
            </span>
          )}
        </div>
      ),
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('rolePermission.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobile ? 100 : 150,
      render: (date: string) => (
        <Text className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
          {isMobile ? formatDate(date).slice(0, 10) : formatDate(date)}
        </Text>
      ),
      sorter: (a: Role, b: Role) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('rolePermission.actions'),
      key: 'actions',
      width: isMobile ? 80 : 200,
      render: (_: any, record: Role) => (
        <Space size='small' direction={isMobile ? 'vertical' : 'horizontal'}>
          <Tooltip title={t('common.edit')}>
            <Button
              type='text'
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
              className='text-blue-600 hover:text-blue-800'
              size={isMobile ? 'small' : 'default'}
            />
          </Tooltip>

          {!isMobile && (
            <Tooltip
              title={
                record.status === 'active'
                  ? t('rolePermission.deactivate')
                  : t('rolePermission.activate')
              }
            >
              <Button
                type='text'
                icon={
                  record.status === 'active' ? (
                    <StopOutlined />
                  ) : (
                    <PlayCircleOutlined />
                  )
                }
                onClick={() => handleToggleStatus(record)}
                className={
                  record.status === 'active'
                    ? 'text-red-600 hover:text-red-800'
                    : 'text-green-600 hover:text-green-800'
                }
                loading={updateStatusMutation.isPending}
                disabled={record.isSystem && record.status === 'active'}
                size={isMobile ? 'small' : 'default'}
              />
            </Tooltip>
          )}

          {!record.isSystem && !isMobile && (
            <Popconfirm
              title={t('rolePermission.deleteConfirmTitle')}
              description={t('rolePermission.deleteConfirmDescription')}
              onConfirm={() => handleDeleteRole(record.id)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
              okButtonProps={{ danger: true }}
              disabled={record.userCount > 0}
            >
              <Tooltip
                title={
                  record.userCount > 0
                    ? t('rolePermission.cannotDeleteWithUsers')
                    : t('common.delete')
                }
              >
                <Button
                  type='text'
                  icon={<DeleteOutlined />}
                  className='text-red-600 hover:text-red-800'
                  loading={deleteRoleMutation.isPending}
                  disabled={record.userCount > 0}
                  size={isMobile ? 'small' : 'default'}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Table
      dataSource={roles}
      columns={columns}
      loading={loading}
      rowKey='id'
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: pagination.onChange,
        showSizeChanger: !isMobile,
        showQuickJumper: !isMobile,
        showTotal: !isMobile ? (total, range) =>
          `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('rolePermission.roles')}` : undefined,
      }}
      scroll={isMobile ? { x: 800 } : undefined}
      size={isMobile ? 'small' : 'default'}
      className='role-list-table'
    />
  );
};

export default RolesList;
