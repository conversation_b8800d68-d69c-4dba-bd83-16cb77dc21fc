import React from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Tooltip,
  <PERSON>confirm,
  Badge,
  Typography,
  Avatar,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  KeyOutlined,
  StopOutlined,
  PlayCircleOutlined,
  CrownOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useUpdateRoleStatus, useDeleteRole } from '@/hooks/useRoles.ts';
import { formatDate } from '@/utils/tableUtils.ts';
import type { Role } from '@/types';

const { Text } = Typography;

interface RolesListProps {
  roles: Role[];
  loading: boolean;
  // @ts-ignore
  onEdit: (role: Role) => void;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    // @ts-ignore
    onChange: (page: number, pageSize: number) => void;
  };
}

const RolesList: React.FC<RolesListProps> = ({
  roles,
  loading,
  onEdit,
  pagination,
}) => {
  const { t } = useTranslation();
  const updateStatusMutation = useUpdateRoleStatus();
  const deleteRoleMutation = useDeleteRole();

  const handleToggleStatus = async (role: Role) => {
    const newStatus = role.status === 'active' ? 'inactive' : 'active';
    updateStatusMutation.mutate({ id: role.id, status: newStatus });
  };

  const handleDeleteRole = async (roleId: string) => {
    deleteRoleMutation.mutate(roleId);
  };

  const getRoleIcon = (role: Role) => {
    if (role.isSystem) {
      return <CrownOutlined className='text-yellow-600' />;
    }
    return <SafetyOutlined className='text-blue-600' />;
  };

  const columns = [
    {
      title: t('rolePermission.roleName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Role) => (
        <div className='flex items-center gap-3'>
          <Avatar
            size={40}
            className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
            icon={getRoleIcon(record)}
          />
          <div>
            <div className='flex items-center gap-2'>
              <Text className='font-medium text-gray-900'>{name}</Text>
              {record.isSystem && (
                <Tag color='gold'>{t('rolePermission.system')}</Tag>
              )}
            </div>
            <Text className='text-sm text-gray-500'>{record.description}</Text>
          </div>
        </div>
      ),
    },
    {
      title: t('rolePermission.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: Role['status']) => (
        <Badge
          status={status === 'active' ? 'success' : 'error'}
          text={
            <span
              className={`font-medium ${
                status === 'active' ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {t(`rolePermission.${status}`)}
            </span>
          }
        />
      ),
    },
    {
      title: t('rolePermission.permissions'),
      dataIndex: 'permissions',
      key: 'permissions',
      width: 150,
      render: (permissions: string[]) => (
        <div className='flex items-center gap-2'>
          <KeyOutlined className='text-blue-500' />
          <span className='font-medium text-blue-600'>
            {permissions.length}
          </span>
          <span className='text-gray-500 text-sm'>
            {t('rolePermission.permissionsCount')}
          </span>
        </div>
      ),
    },
    {
      title: t('rolePermission.assignedUsers'),
      dataIndex: 'userCount',
      key: 'userCount',
      width: 150,
      render: (userCount: number) => (
        <div className='flex items-center gap-2'>
          <UserOutlined className='text-green-500' />
          <span className='font-medium text-green-600'>{userCount}</span>
          <span className='text-gray-500 text-sm'>
            {userCount === 1
              ? t('rolePermission.user')
              : t('rolePermission.users')}
          </span>
        </div>
      ),
    },
    {
      title: t('rolePermission.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <Text className='text-gray-600'>{formatDate(date)}</Text>
      ),
      sorter: (a: Role, b: Role) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: t('rolePermission.actions'),
      key: 'actions',
      width: 200,
      render: (_: any, record: Role) => (
        <Space size='small'>
          <Tooltip title={t('common.edit')}>
            <Button
              type='text'
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
              className='text-blue-600 hover:text-blue-800'
            />
          </Tooltip>

          <Tooltip
            title={
              record.status === 'active'
                ? t('rolePermission.deactivate')
                : t('rolePermission.activate')
            }
          >
            <Button
              type='text'
              icon={
                record.status === 'active' ? (
                  <StopOutlined />
                ) : (
                  <PlayCircleOutlined />
                )
              }
              onClick={() => handleToggleStatus(record)}
              className={
                record.status === 'active'
                  ? 'text-red-600 hover:text-red-800'
                  : 'text-green-600 hover:text-green-800'
              }
              loading={updateStatusMutation.isPending}
              disabled={record.isSystem && record.status === 'active'}
            />
          </Tooltip>

          {!record.isSystem && (
            <Popconfirm
              title={t('rolePermission.deleteConfirmTitle')}
              description={t('rolePermission.deleteConfirmDescription')}
              onConfirm={() => handleDeleteRole(record.id)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
              okButtonProps={{ danger: true }}
              disabled={record.userCount > 0}
            >
              <Tooltip
                title={
                  record.userCount > 0
                    ? t('rolePermission.cannotDeleteWithUsers')
                    : t('common.delete')
                }
              >
                <Button
                  type='text'
                  icon={<DeleteOutlined />}
                  className='text-red-600 hover:text-red-800'
                  loading={deleteRoleMutation.isPending}
                  disabled={record.userCount > 0}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Table
      dataSource={roles}
      columns={columns}
      loading={loading}
      rowKey='id'
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: pagination.onChange,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('rolePermission.roles')}`,
      }}
      className='role-list-table'
    />
  );
};

export default RolesList;
