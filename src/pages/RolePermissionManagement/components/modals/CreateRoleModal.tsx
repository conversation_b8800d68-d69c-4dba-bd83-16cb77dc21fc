import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Checkbox,
  Card,
  Collapse,
  Typography,
  Tag,
  Tooltip,
  Button,
  Divider,
} from 'antd';
import {
  SaveOutlined,
  KeyOutlined,
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  SafetyOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ControlOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useCreateRole } from '@/hooks/useRoles.ts';
import type {
  Permission,
  PermissionCategory,
  CreateRoleRequest,
} from '@/types';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

interface CreateRoleModalProps {
  visible: boolean;
  onClose: () => void;
  permissions: Permission[];
  categories: PermissionCategory[];
}

const CreateRoleModal: React.FC<CreateRoleModalProps> = ({
  visible,
  onClose,
  permissions,
  categories,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const createRoleMutation = useCreateRole();

  const getCategoryIcon = (iconName?: string) => {
    switch (iconName) {
      case 'UserOutlined':
        return <UserOutlined className='text-blue-600' />;
      case 'DollarOutlined':
        return <DollarOutlined className='text-green-600' />;
      case 'TrophyOutlined':
        return <TrophyOutlined className='text-yellow-600' />;
      case 'SafetyOutlined':
        return <SafetyOutlined className='text-red-600' />;
      case 'BarChartOutlined':
        return <BarChartOutlined className='text-purple-600' />;
      case 'SettingOutlined':
        return <SettingOutlined className='text-gray-600' />;
      default:
        return <KeyOutlined className='text-blue-600' />;
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'view':
        return <EyeOutlined className='text-blue-500' />;
      case 'create':
        return <PlusOutlined className='text-green-500' />;
      case 'edit':
        return <EditOutlined className='text-orange-500' />;
      case 'delete':
        return <DeleteOutlined className='text-red-500' />;
      case 'manage':
        return <ControlOutlined className='text-purple-500' />;
      default:
        return <KeyOutlined className='text-gray-500' />;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'view':
        return 'blue';
      case 'create':
        return 'green';
      case 'edit':
        return 'orange';
      case 'delete':
        return 'red';
      case 'manage':
        return 'purple';
      default:
        return 'default';
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => id !== permissionId),
      );
    }
  };

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    if (checked) {
      const newPermissions = [
        ...new Set([...selectedPermissions, ...categoryPermissions]),
      ];
      setSelectedPermissions(newPermissions);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => !categoryPermissions.includes(id)),
      );
    }
  };

  const isCategorySelected = (categoryId: string) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    return categoryPermissions.every((id) => selectedPermissions.includes(id));
  };

  const isCategoryIndeterminate = (categoryId: string) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    const selectedCount = categoryPermissions.filter((id) =>
      selectedPermissions.includes(id),
    ).length;
    return selectedCount > 0 && selectedCount < categoryPermissions.length;
  };
  const handleSubmit = async (values: any) => {
    const roleData: CreateRoleRequest = {
      name: values.name,
      description: values.description,
      permissions: selectedPermissions,
    };

    try {
      await createRoleMutation.mutateAsync(roleData);
      form.resetFields();
      setSelectedPermissions([]);
      onClose();
    } catch (error) {
      console.error('Failed to create role:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedPermissions([]);
    onClose();
  };

  // Group permissions by category
  const permissionsByCategory = categories.map((category) => ({
    category,
    permissions: permissions.filter((p) => p.category.id === category.id),
  }));

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <KeyOutlined className='text-blue-600' />
          {t('rolePermission.createRole')}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key='cancel' onClick={handleCancel}>
          {t('common.cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={createRoleMutation.isPending}
          onClick={() => form.submit()}
          icon={<SaveOutlined />}
          className='bg-blue-600 hover:bg-blue-700'
        >
          {t('rolePermission.createRole')}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        className='max-h-[70vh] overflow-y-auto'
      >
        {/* Basic Information */}
        <div className='mb-6'>
          <Title level={5} className='text-gray-900 mb-4'>
            {t('rolePermission.basicInformation')}
          </Title>

          <Form.Item
            name='name'
            label={t('rolePermission.roleName')}
            rules={[
              { required: true, message: t('rolePermission.roleNameRequired') },
              { min: 2, message: t('rolePermission.roleNameMinLength') },
              { max: 50, message: t('rolePermission.roleNameMaxLength') },
            ]}
          >
            <Input
              placeholder={t('rolePermission.roleNamePlaceholder')}
              size='large'
            />
          </Form.Item>

          <Form.Item
            name='description'
            label={t('rolePermission.roleDescription')}
            rules={[
              {
                required: true,
                message: t('rolePermission.roleDescriptionRequired'),
              },
              {
                max: 200,
                message: t('rolePermission.roleDescriptionMaxLength'),
              },
            ]}
          >
            <TextArea
              rows={3}
              placeholder={t('rolePermission.roleDescriptionPlaceholder')}
              size='large'
            />
          </Form.Item>
        </div>

        <Divider />

        {/* Permission Selection */}
        <div className='mb-6'>
          <div className='flex items-center justify-between mb-4'>
            <Title level={5} className='text-gray-900 mb-0'>
              {t('rolePermission.selectPermissions')}
            </Title>
            <Tag color='blue' className='font-medium'>
              {selectedPermissions.length} {t('rolePermission.selected')}
            </Tag>
          </div>

          <Paragraph className='text-gray-600 mb-4'>
            {t('rolePermission.selectPermissionsDescription')}
          </Paragraph>

          <Collapse
            defaultActiveKey={categories.map((c) => c.id)}
            className='permission-selection-collapse'
          >
            {permissionsByCategory.map(
              ({ category, permissions: categoryPermissions }) => (
                <Panel
                  key={category.id}
                  header={
                    <div className='flex items-center justify-between w-full pr-4'>
                      <div className='flex items-center gap-3'>
                        <Checkbox
                          checked={isCategorySelected(category.id)}
                          indeterminate={isCategoryIndeterminate(category.id)}
                          onChange={(e) =>
                            handleCategoryChange(category.id, e.target.checked)
                          }
                          onClick={(e) => e.stopPropagation()}
                        />
                        {getCategoryIcon(category.icon)}
                        <div>
                          <Text className='font-medium'>{category.name}</Text>
                          <div className='text-sm text-gray-500'>
                            {category.description}
                          </div>
                        </div>
                      </div>
                      <Tag color='blue' >
                        {categoryPermissions.length}{' '}
                        {t('rolePermission.permissions')}
                      </Tag>
                    </div>
                  }
                >
                  <div className='space-y-4 pl-8'>
                    {categoryPermissions.map((permission) => (
                      <Card
                        key={permission.id}
                        size='small'
                        className='border border-gray-200'
                      >
                        <div className='flex items-start gap-3'>
                          <Checkbox
                            checked={selectedPermissions.includes(
                              permission.id,
                            )}
                            onChange={(e) =>
                              handlePermissionChange(
                                permission.id,
                                e.target.checked,
                              )
                            }
                          />
                          <div className='flex-1'>
                            <div className='flex items-center gap-2 mb-1'>
                              <KeyOutlined className='text-blue-500 text-sm' />
                              <Text className='font-medium'>
                                {permission.name}
                              </Text>
                            </div>
                            <Paragraph className='text-gray-600 mb-2 text-sm'>
                              {permission.description}
                            </Paragraph>
                            <div className='flex flex-wrap gap-1'>
                              {permission.actions.map((action) => (
                                <Tooltip
                                  key={action.id}
                                  title={action.description}
                                  placement='top'
                                >
                                  <Tag
                                    color={getActionColor(action.type)}
                                    className='flex items-center gap-1'
                                  >
                                    {getActionIcon(action.type)}
                                    {action.name}
                                  </Tag>
                                </Tooltip>
                              ))}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </Panel>
              ),
            )}
          </Collapse>
        </div>
      </Form>
    </Modal>
  );
};

export default CreateRoleModal;
