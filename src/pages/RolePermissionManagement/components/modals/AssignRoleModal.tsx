import React, { useState } from 'react';
import {
  Modal,
  Form,
  Select,
  Card,
  Typography,
  Tag,
  Button,
  Avatar,
  Divider,
  Alert,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  KeyOutlined,
  MailOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAssignUserRole } from '@/hooks/useRoles.ts';
import { useEmployees } from '@/hooks/useManagement.ts';
import type { Role } from '@/types';

const { Title, Paragraph } = Typography;
const { Option } = Select;

interface AssignRoleModalProps {
  visible: boolean;
  onClose: () => void;
  roles: Role[];
}

const AssignRoleModal: React.FC<AssignRoleModalProps> = ({
  visible,
  onClose,
  roles,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  // @ts-ignore
  const [selectedUser, setSelectedUser] = useState<string>('');
  const assignRoleMutation = useAssignUserRole();

  // Fetch employees for user selection
  const { data: employeesResponse } = useEmployees({ pageSize: 100 });
  const employees = employeesResponse?.data?.data || [];

  const handleRoleChange = (roleId: string) => {
    const role = roles.find((r) => r.id === roleId);
    setSelectedRole(role || null);
  };

  const handleSubmit = async (values: any) => {
    try {
      await assignRoleMutation.mutateAsync({
        userId: values.userId,
        roleId: values.roleId,
      });
      form.resetFields();
      setSelectedRole(null);
      setSelectedUser('');
      onClose();
    } catch (error) {
      console.error('Failed to assign role:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedRole(null);
    setSelectedUser('');
    onClose();
  };

  const getRoleColor = (role: Role) => {
    if (role.isSystem) return 'gold';
    return 'blue';
  };

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <TeamOutlined className='text-green-600' />
          {t('rolePermission.assignRole')}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key='cancel' onClick={handleCancel}>
          {t('common.cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={assignRoleMutation.isPending}
          onClick={() => form.submit()}
          icon={<UserOutlined />}
          className='bg-green-600 hover:bg-green-700'
        >
          {t('rolePermission.assignRole')}
        </Button>,
      ]}
    >
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Alert
          message={t('rolePermission.assignRoleInfo')}
          description={t('rolePermission.assignRoleInfoDescription')}
          type='info'
          className='mb-6'
          showIcon
        />

        {/* User Selection */}
        <Form.Item
          name='userId'
          label={t('rolePermission.selectUser')}
          rules={[
            { required: true, message: t('rolePermission.userRequired') },
          ]}
        >
          <Select
            placeholder={t('rolePermission.selectUserPlaceholder')}
            size='large'
            showSearch
            optionFilterProp='children'
            style={{height: '70px', caretColor: 'transparent'}}
            filterOption={(input, option) =>
              (option?.children as unknown as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
            onChange={setSelectedUser}
          >
            {employees.map((employee) => (
              <Option key={employee.id} value={employee.id}>
                <div className='flex items-center gap-3 py-1'>
                  <Avatar
                    size={32}
                    className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
                  >
                    {employee.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <div>
                    <div className='font-medium'>{employee.name}</div>
                    <div className='text-sm text-gray-500 flex items-center gap-1'>
                      <MailOutlined className='text-xs' />
                      {employee.email}
                    </div>
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Role Selection */}
        <Form.Item
          name='roleId'
          label={t('rolePermission.selectRole')}
          rules={[
            { required: true, message: t('rolePermission.roleRequired') },
          ]}
        >
          <Select
            placeholder={t('rolePermission.selectRolePlaceholder')}
            size='large'
            style={{height: '70px', caretColor: 'transparent'}}
            onChange={handleRoleChange}
          >
            {roles.map((role) => (
              <Option key={role.id} value={role.id}>
                <div className='flex items-center gap-3 py-1'>
                  <SafetyOutlined className='text-blue-500' />
                  <div>
                    <div className='flex items-center gap-2'>
                      <span className='font-medium'>{role.name}</span>
                      <Tag color={getRoleColor(role)}>
                        {role.isSystem
                          ? t('rolePermission.system')
                          : t('rolePermission.custom')}
                      </Tag>
                    </div>
                    <div className='text-sm text-gray-500'>
                      {role.description}
                    </div>
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Role Details */}
        {selectedRole && (
          <>
            <Divider />
            <div className='mb-4'>
              <Title level={5} className='text-gray-900 mb-3'>
                {t('rolePermission.roleDetails')}
              </Title>

              <Card className='border border-blue-200 bg-blue-50/50'>
                <div className='flex items-start gap-3'>
                  <SafetyOutlined className='text-blue-600 text-xl mt-1' />
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-2'>
                      <Title level={5} className='mb-0'>
                        {selectedRole.name}
                      </Title>
                      <Tag color={getRoleColor(selectedRole)}>
                        {selectedRole.isSystem
                          ? t('rolePermission.system')
                          : t('rolePermission.custom')}
                      </Tag>
                    </div>

                    <Paragraph className='text-gray-600 mb-3'>
                      {selectedRole.description}
                    </Paragraph>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='flex items-center gap-2'>
                        <KeyOutlined className='text-blue-500' />
                        <div>
                          <div className='font-medium text-blue-600'>
                            {selectedRole.permissions.length}
                          </div>
                          <div className='text-sm text-gray-500'>
                            {t('rolePermission.permissions')}
                          </div>
                        </div>
                      </div>
                      <div className='flex items-center gap-2'>
                        <UserOutlined className='text-green-500' />
                        <div>
                          <div className='font-medium text-green-600'>
                            {selectedRole.userCount}
                          </div>
                          <div className='text-sm text-gray-500'>
                            {t('rolePermission.assignedUsers')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </>
        )}
      </Form>
    </Modal>
  );
};

export default AssignRoleModal;
