import React from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Tooltip,
  Popconfirm,
  Avatar,
  Typography,
} from 'antd';
import {
  UserOutlined,
  DeleteOutlined,
  MailOutlined,
  CalendarOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useRemoveUserRole } from '@/hooks/useRoles.ts';
import { formatDate } from '@/utils/tableUtils.ts';
import type { UserRole, Role } from '@/types';

const { Text } = Typography;

interface UserRolesListProps {
  userRoles: UserRole[];
  roles: Role[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    //@ts-ignore
    onChange: (page: number, pageSize: number) => void;
  };
}

const UserRolesList: React.FC<UserRolesListProps> = ({
  userRoles,
  roles,
  loading,
  pagination,
}) => {
  const { t } = useTranslation();
  const removeUserRoleMutation = useRemoveUserRole();

  const handleRemoveRole = async (userRoleId: string) => {
    removeUserRoleMutation.mutate(userRoleId);
  };

  const getRoleInfo = (roleId: string) => {
    return roles.find((role) => role.id === roleId);
  };

  const getRoleColor = (roleId: string) => {
    const role = getRoleInfo(roleId);
    if (!role) return 'default';

    if (role.isSystem) return 'gold';
    if (role.status === 'active') return 'green';
    return 'red';
  };

  const columns = [
    {
      title: t('rolePermission.user'),
      key: 'user',
      render: (_: any, record: UserRole) => (
        <div className='flex items-center gap-3'>
          <Avatar
            size={40}
            className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
            icon={<UserOutlined />}
          >
            {record.userName.charAt(0).toUpperCase()}
          </Avatar>
          <div>
            <Text className='font-medium text-gray-900 block'>
              {record.userName}
            </Text>
            <div className='flex items-center gap-1 text-sm text-gray-500'>
              <MailOutlined className='text-xs' />
              {record.userEmail}
            </div>
            <Text className='text-xs text-gray-400'>ID: {record.userId}</Text>
          </div>
        </div>
      ),
    },
    {
      title: t('rolePermission.assignedRole'),
      key: 'role',
      render: (_: any, record: UserRole) => {
        const role = getRoleInfo(record.roleId);
        return (
          <div className='flex items-center gap-2'>
            <SafetyOutlined className='text-blue-500' />
            <div>
              <Tag color={getRoleColor(record.roleId)} className='font-medium'>
                {record.roleName}
              </Tag>
              {role && (
                <div className='text-xs text-gray-500 mt-1'>
                  {role.permissions.length} {t('rolePermission.permissions')}
                  {role.isSystem && (
                    <span className='ml-2 text-yellow-600'>
                      • {t('rolePermission.system')}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: t('rolePermission.assignedAt'),
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      width: 150,
      render: (date: string) => (
        <div className='flex items-center gap-2'>
          <CalendarOutlined className='text-gray-400' />
          <Text className='text-gray-600'>{formatDate(date)}</Text>
        </div>
      ),
      sorter: (a: UserRole, b: UserRole) =>
        new Date(a.assignedAt).getTime() - new Date(b.assignedAt).getTime(),
    },
    {
      title: t('rolePermission.assignedBy'),
      dataIndex: 'assignedBy',
      key: 'assignedBy',
      width: 120,
      render: (assignedBy: string) => (
        <Text className='text-gray-600'>{assignedBy}</Text>
      ),
    },
    {
      title: t('rolePermission.actions'),
      key: 'actions',
      width: 100,
      render: (_: any, record: UserRole) => {
        const role = getRoleInfo(record.roleId);
        const isSystemRole = role?.isSystem;

        return (
          <Space size='small'>
            <Popconfirm
              title={t('rolePermission.removeRoleConfirmTitle')}
              description={t('rolePermission.removeRoleConfirmDescription')}
              onConfirm={() => handleRemoveRole(record.id)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
              okButtonProps={{ danger: true }}
              disabled={isSystemRole}
            >
              <Tooltip
                title={
                  isSystemRole
                    ? t('rolePermission.cannotRemoveSystemRole')
                    : t('rolePermission.removeRole')
                }
              >
                <Button
                  type='text'
                  icon={<DeleteOutlined />}
                  className='text-red-600 hover:text-red-800'
                  loading={removeUserRoleMutation.isPending}
                  disabled={isSystemRole}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <Table
      dataSource={userRoles}
      columns={columns}
      loading={loading}
      rowKey='id'
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: pagination.onChange,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('rolePermission.assignments')}`,
      }}
      className='user-roles-table'
    />
  );
};

export default UserRolesList;
